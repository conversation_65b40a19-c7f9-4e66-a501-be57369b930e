package com.weaver.seconddev.zyhlw.service.impl;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.weaver.datasource.utils.rest.CommonRestService;
import com.weaver.ebuilder.datasource.api.entity.ExecuteSqlEntity;
import com.weaver.ebuilder.datasource.api.entity.SqlParamEntity;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.ebuilder.datasource.api.enums.SqlParamType;
import com.weaver.ebuilder.datasource.api.service.DataSetService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataSqlServiceImpl implements IDataSqlService {
    @Resource
    DataSetService dataSetService;

    @Resource
    private CommonRestService commonRestService;

    private final String simpleName = DataSqlServiceImpl.class.getSimpleName();
    private Logger logger = LoggerFactory.getLogger(DataSqlServiceImpl.class);

    /**
     * 查询weaver-ebuilder-form-service分组数据
     *
     * @param sql        分组的唯一标识
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)
     * @return 结果返回records
     */
    @Override
    public List<Map<String, Object>> ebuilderFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType) throws Exception {
        String methodName = "调用" + simpleName + ".ebuilderFromSql()";
        ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
        executeSqlEntity.setSql(Base64.encode(sql));
        executeSqlEntity.setGroupId("weaver-ebuilder-form-service");
        executeSqlEntity.setSourceType(sourceType);
        executeSqlEntity.setPageNo(pageNo);
        executeSqlEntity.setPageSize(pageSize);
        logger.info("{} 查询weaver-ebuilder-form-service分组数据，sql：{}，pageNo：{}，pageSize：{}，SourceType：{}", methodName, sql, pageNo, pageSize, sourceType);
        Map<String, Object> sqlData = dataSetService.executeSql(executeSqlEntity);

        logger.info("{} 查询weaver-ebuilder-form-service分组数据结果：{}", methodName, JSON.toJSONString(sqlData));
        String recordsKey = "records";
        List<Map<String, Object>> fenLpzRecords = new ArrayList<>();
        if (sqlData.containsKey(recordsKey)) {
            fenLpzRecords.addAll((List<Map<String, Object>>) sqlData.get(recordsKey));
        }
        return fenLpzRecords;
    }

    /**
     * 查询weaver-ebuilder-form-service分组数据
     *
     * @param sql        分组的唯一标识
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param sourceType 数据源类型(目前仅支持LOGIC和EXTERNAL类型)
     * @param paramsList 参数集合, 一定要和SQL中的？参数一一对应
     * @return 结果返回records
     */
    @Override
    public List<Map<String, Object>> ebuilderFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType, List<String> paramsList) throws Exception {
        String methodName = "调用" + simpleName + ".ebuilderFromSql()";
        ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
        executeSqlEntity.setSql(Base64.encode(sql));
        executeSqlEntity.setGroupId("weaver-ebuilder-form-service");
        executeSqlEntity.setSourceType(sourceType);
        executeSqlEntity.setPageNo(pageNo);
        executeSqlEntity.setPageSize(pageSize);
        List<SqlParamEntity> sqlParamEntityList = getSqlParamEntities(paramsList);
        if (!sqlParamEntityList.isEmpty()) {
            executeSqlEntity.setParams(sqlParamEntityList);
        }
        logger.info("{} 查询weaver-ebuilder-form-service分组数据，sql：{}，pageNo：{}，pageSize：{}，SourceType：{}，List<String>：{}", methodName, sql, pageNo, pageSize, sourceType, paramsList);
        Map<String, Object> sqlData = dataSetService.executeSql(executeSqlEntity);

        logger.info("{} 查询weaver-ebuilder-form-service分组数据结果：{}", methodName, JSON.toJSONString(sqlData));
        String recordsKey = "records";
        List<Map<String, Object>> fenLpzRecords = new ArrayList<>();
        if (sqlData.containsKey(recordsKey)) {
            fenLpzRecords.addAll((List<Map<String, Object>>) sqlData.get(recordsKey));
        }
        return fenLpzRecords;
    }

    @Override
    public List<Map<String, Object>> workflowFromSql(String sql, Integer pageNo, Integer pageSize, SourceType sourceType, List<String> paramsList) throws Exception {
        String methodName = "调用" + simpleName + ".workflowFromSql()";
        ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
        executeSqlEntity.setSql(Base64.encode(sql));
        executeSqlEntity.setGroupId("weaver-workflow-report-serviceWorkflowReport".toLowerCase());
        executeSqlEntity.setSourceType(sourceType);
        executeSqlEntity.setPageNo(pageNo);
        executeSqlEntity.setPageSize(pageSize);
        List<SqlParamEntity> sqlParamEntityList = getSqlParamEntities(paramsList);
        if (!sqlParamEntityList.isEmpty()) {
            executeSqlEntity.setParams(sqlParamEntityList);
        }
        logger.info("{} 查询weaver-workflow-report-serviceWorkflowReport分组数据，sql：{}，pageNo：{}，pageSize：{}，SourceType：{}", methodName, sql, pageNo, pageSize, sourceType);
        Map<String, Object> sqlData = dataSetService.executeSql(executeSqlEntity);

        logger.info("{} 查询weaver-workflow-report-serviceWorkflowReport分组数据结果：{}", methodName, JSON.toJSONString(sqlData));
        String recordsKey = "records";
        List<Map<String, Object>> fenLpzRecords = new ArrayList<>();
        if (sqlData.containsKey(recordsKey)) {
            fenLpzRecords.addAll((List<Map<String, Object>>) sqlData.get(recordsKey));
        }
        return fenLpzRecords;
    }

    @Override
    public List<Map<String, Object>> eBuilderFromSqlAll(String sql, SourceType sourceType) {
        logger.info("eBuilderFromSqlAll查询，当前sql：{}", sql);
        int pageSize = 1000;
        int pageNumber = 1;
        List<Map<String, Object>> dataResult = new ArrayList<>();
        while (true) {
            try {
                List<Map<String, Object>> dataList = ebuilderFromSql(sql, pageNumber, pageSize, sourceType);
                if (dataList.isEmpty()) {
                    logger.info("eBuilderFromSqlAll查询，当前页：{}，返回结果为空，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                dataResult.addAll(dataList);
                if (dataList.size() < pageSize) {
                    logger.info("eBuilderFromSqlAll查询，当前页：{}，查询结束，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                pageNumber++;
            } catch (Exception e) {
                logger.info("eBuilderFromSqlAll查询，当前页：{}，发生异常: {}", pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return dataResult;
    }

    @Override
    public List<Map<String, Object>> eBuilderFromSqlAll(String sql, SourceType sourceType, List<String> paramsList) {
        logger.info("eBuilderFromSqlAll查询，当前sql：{}，参数：{}", sql, paramsList);
        int pageSize = 1000;
        int pageNumber = 1;
        List<Map<String, Object>> dataResult = new ArrayList<>();
        while (true) {
            try {
                List<Map<String, Object>> dataList = ebuilderFromSql(sql, pageNumber, pageSize, sourceType, paramsList);
                if (dataList.isEmpty()) {
                    logger.info("eBuilderFromSqlAll查询，当前页：{}，返回结果为空，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                dataResult.addAll(dataList);
                if (dataList.size() < pageSize) {
                    logger.info("eBuilderFromSqlAll查询，当前页：{}，查询结束，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                pageNumber++;
            } catch (Exception e) {
                logger.info("eBuilderFromSqlAll查询，当前页：{}，发生异常: {}", pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return dataResult;
    }

    @Override
    public Map<String, Object> eBuilderFromSqlOne(String sql, SourceType sourceType) {
        try {
            List<Map<String, Object>> dataList = ebuilderFromSql(sql, 1, 1, sourceType);
            if (!dataList.isEmpty()) {
                return dataList.get(0);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Collections.emptyMap();
    }

    @Override
    public List<Map<String, Object>> workflowFromSqlAll(String sql, SourceType sourceType) {
        logger.info("workflowFromSqlAll查询，当前sql：{}", sql);
        int pageSize = 1000;
        int pageNumber = 1;
        List<Map<String, Object>> dataResult = new ArrayList<>();
        while (true) {
            try {
                List<Map<String, Object>> dataList = workflowFromSql(sql, pageNumber, pageSize, sourceType, new ArrayList<>());
                if (dataList.isEmpty()) {
                    logger.info("workflowFromSqlAll查询，当前页：{}，返回结果为空，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                dataResult.addAll(dataList);
                if (dataList.size() < pageSize) {
                    logger.info("workflowFromSqlAll查询，当前页：{}，查询结束，结果总数：{}", pageNumber, dataResult.size());
                    break;
                }
                pageNumber++;
            } catch (Exception e) {
                logger.info("workflowFromSqlAll查询，当前页：{}，发生异常: {}", pageNumber, e);
                throw new RuntimeException(e);
            }
        }
        return dataResult;
    }

    @Override
    public Map<String, Object> workflowFromSqlOne(String sql, SourceType sourceType) {
        try {
            List<Map<String, Object>> dataList = workflowFromSql(sql, 1, 1, sourceType, new ArrayList<>());
            if (!dataList.isEmpty()) {
                return dataList.get(0);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Collections.emptyMap();
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> executeCommonSqlOne(String querySql, SourceType sourceType,
                                                   String groupId, List<String> paramsList) {
        String methodName = "调用" + simpleName + ".commonSqlOne()";
        try {
            ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
            executeSqlEntity.setSql(Base64.encode(querySql));
            executeSqlEntity.setGroupId(groupId);
            executeSqlEntity.setSourceType(sourceType);
            executeSqlEntity.setPageNo(1);
            executeSqlEntity.setPageSize(1);

            // 处理参数集合
            if (paramsList != null && !paramsList.isEmpty()) {
                List<SqlParamEntity> sqlParamEntityList = getSqlParamEntities(paramsList);
                executeSqlEntity.setParams(sqlParamEntityList);
            }

            logger.info("{} 通用查询，sql：{}，groupId：{}，SourceType：{}，paramsList：{}", methodName, querySql, groupId, sourceType, paramsList);
            Map<String, Object> sqlData = dataSetService.executeSql(executeSqlEntity);

            logger.info("{} 通用查询结果：{}", methodName, JSON.toJSONString(sqlData));
            String recordsKey = "records";
            if (sqlData.containsKey(recordsKey)) {
                List<Map<String, Object>> records = new ArrayList<>((List<Map<String, Object>>)
                        sqlData.get(recordsKey));
                if (!records.isEmpty()) {
                    return records.get(0);
                }
            }
        } catch (Exception e) {
            logger.error("{} 通用查询异常：{}", methodName, e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> executeSqlForWithTrans(String sql, SourceType sourceType, String groupId,
                                                      List<String> paramsList, String transId,
                                                      Boolean startTrans, Boolean commit, Boolean rollback) {
        String methodName = "调用" + simpleName + ".executeSqlForWithTrans()";
        try {
            //执行sql 参数sourceType groupId sql
            ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
            executeSqlEntity.setSql(Base64.encode(sql));
            executeSqlEntity.setGroupId(groupId);
            executeSqlEntity.setSourceType(sourceType);

            //若通过占位符方式查询外部数据库,需增加此参数
            //占位符list 的顺序 要与 sql 的?占位符顺序一致
            if (paramsList != null && !paramsList.isEmpty()) {
                List<SqlParamEntity> sqlParamEntityList = getSqlParamEntities(paramsList);
                executeSqlEntity.setParams(sqlParamEntityList);
            }

            //设置事务id
            if (transId != null && !transId.isEmpty()) {
                executeSqlEntity.setTransactionId(transId);
            }
            //是否开启事务
            if (startTrans != null && startTrans) {
                executeSqlEntity.setStartTransaction(true);
            }
            //是否提交事务
            if (commit != null && commit) {
                executeSqlEntity.setCommitTransaction(true);
            }
            //是否回滚事务
            if (rollback != null && rollback) {
                executeSqlEntity.setRollbackTransaction(true);
            }

            logger.info("{} 带事务执行sql，sql：{}，groupId：{}，SourceType：{}，paramsList：{}，transId：{}，startTrans：{}，commit：{}，rollback：{}",
                    methodName, sql, groupId, sourceType, paramsList, transId, startTrans, commit, rollback);

            // 使用dataSetService.executeSql
            Map<String, Object> result = dataSetService.executeForUpdate(executeSqlEntity);

            logger.info("{} 带事务执行sql结果：{}", methodName, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.error("{} 带事务执行sql异常：{}", methodName, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private List<SqlParamEntity> getSqlParamEntities(List<String> paramsList) {
        if (CollectionUtils.isEmpty(paramsList)) {
            return Collections.emptyList();
        }
        List<SqlParamEntity> sqlParamEntityList = new ArrayList<>();
        for (String paramStr : paramsList) {
            SqlParamEntity sqlParam = new SqlParamEntity();
            sqlParam.setParamType(SqlParamType.VARCHAR);
            sqlParam.setValue(String.valueOf(paramStr));
            sqlParamEntityList.add(sqlParam);
        }
        return sqlParamEntityList;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> executeCommonSqlAll(String querySql, SourceType sourceType, String groupId,
                                                         List<String> paramsList) {
        String methodName = "调用" + simpleName + ".executeCommonSqlAll()";
        logger.info("{} 通用查询全部数据，sql：{}，groupId：{}，SourceType：{}，paramsList：{}",
                methodName, querySql, groupId, sourceType, paramsList);

        int pageSize = 1000;
        int pageNumber = 1;
        List<Map<String, Object>> dataResult = new ArrayList<>();

        while (true) {
            try {
                // 构建查询实体
                ExecuteSqlEntity executeSqlEntity = new ExecuteSqlEntity();
                executeSqlEntity.setSql(Base64.encode(querySql));
                executeSqlEntity.setGroupId(groupId);
                executeSqlEntity.setSourceType(sourceType);
                executeSqlEntity.setPageNo(pageNumber);
                executeSqlEntity.setPageSize(pageSize);

                // 处理参数集合
                if (paramsList != null && !paramsList.isEmpty()) {
                    List<SqlParamEntity> sqlParamEntityList = getSqlParamEntities(paramsList);
                    executeSqlEntity.setParams(sqlParamEntityList);
                }

                // 执行查询
                Map<String, Object> sqlData = dataSetService.executeSql(executeSqlEntity);

                // 处理结果
                String recordsKey = "records";
                List<Map<String, Object>> currentPageData = new ArrayList<>();
                if (sqlData.containsKey(recordsKey)) {
                    currentPageData.addAll((List<Map<String, Object>>) sqlData.get(recordsKey));
                }

                // 判断是否有数据
                if (currentPageData.isEmpty()) {
                    logger.info("{} 通用查询全部数据，当前页：{}，返回结果为空，结果总数：{}",
                            methodName, pageNumber, dataResult.size());
                    break;
                }

                // 添加到结果集
                dataResult.addAll(currentPageData);

                // 判断是否还有下一页
                if (currentPageData.size() < pageSize) {
                    logger.info("{} 通用查询全部数据，当前页：{}，查询结束，结果总数：{}",
                            methodName, pageNumber, dataResult.size());
                    break;
                }

                // 下一页
                pageNumber++;
            } catch (Exception e) {
                logger.error("{} 通用查询全部数据，当前页：{}，发生异常: {}",
                        methodName, pageNumber, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }

        return dataResult;
    }
}

