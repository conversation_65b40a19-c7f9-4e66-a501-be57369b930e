package com.weaver.seconddev.zyhlw.service.impl.appeal;

import cn.hutool.core.map.MapUtil;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealHelpDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListDTO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealListRespVO;
import com.weaver.seconddev.zyhlw.domain.appeal.AppealTypeListRespVO;
import com.weaver.seconddev.zyhlw.domain.request.eb.EbFormDataReq;
import com.weaver.seconddev.zyhlw.service.IAppealCenterService;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.service.IOpenPlatformService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.ConvertDataUtils;
import com.weaver.seconddev.zyhlw.util.StringUtils;
import com.weaver.seconddev.zyhlw.util.ebuilder.EBuilderUtil;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import com.weaver.teams.domain.user.SimpleEmployee;
import com.weaver.teams.security.context.UserContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 诉求中心服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
@Slf4j
public class AppealCenterServiceImpl implements IAppealCenterService {

    @Resource
    private IDataSqlService dataSqlService;

    @Resource
    private IDataBaseService dataBaseService;

    @Resource
    private CmicProperties cmicProperties;

    @Resource
    private IOpenPlatformService openPlatformService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Override
    public List<AppealTypeListRespVO> getTypeList(String id) {
        List<String> sqlParams = new ArrayList<>();
        String where = null;
        if (StringUtils.isNotEmpty(id)) {
            where += " and id = ? ";
            sqlParams.add(id);
        } else {
            where = "  is null ";
        }
        String querySql = "select id,fen_lmc from uf_fen_lwh where zhuang_t = 0 and gui_ssjfl " + where
                + " order by pai_x asc";

        List<Map<String, Object>> result = ConvertDataUtils
                .convertListMapKeyToLowerCase(dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC, sqlParams));

        List<AppealTypeListRespVO> resultList = new ArrayList<>();
        result.forEach(item -> {
            AppealTypeListRespVO appealTypeListRespVO = AppealTypeListRespVO.builder()
                    .id(MapUtil.getStr(item, "id"))
                    .name(StringUtils.null2String(MapUtil.getStr(item, "fen_lmc")))
                    .build();
            // 获取子节点
            List<AppealTypeListRespVO> children = getChildrenTypes(MapUtil.getStr(item, "id"));
            if (!children.isEmpty()) {
                appealTypeListRespVO.setChildren(children);
            }
            resultList.add(appealTypeListRespVO);
        });
        return resultList;
    }

    /**
     * 递归获取子节点
     * 
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    private List<AppealTypeListRespVO> getChildrenTypes(String parentId) {
        if (StringUtils.isEmpty(parentId)) {
            return new ArrayList<>();
        }

        List<String> sqlParams = new ArrayList<>();
        String querySql = "select id,fen_lmc from uf_fen_lwh where zhuang_t = 0 and gui_ssjfl = ? order by pai_x asc";
        sqlParams.add(parentId);

        List<Map<String, Object>> result = ConvertDataUtils
                .convertListMapKeyToLowerCase(dataSqlService.eBuilderFromSqlAll(querySql, SourceType.LOGIC, sqlParams));

        List<AppealTypeListRespVO> childrenList = new ArrayList<>();
        result.forEach(item -> {
            AppealTypeListRespVO childType = AppealTypeListRespVO.builder()
                    .id(MapUtil.getStr(item, "id"))
                    .name(StringUtils.null2String(MapUtil.getStr(item, "fen_lmc")))
                    .build();

            // 递归获取子节点的子节点
            List<AppealTypeListRespVO> grandChildren = getChildrenTypes(MapUtil.getStr(item, "id"));
            if (!grandChildren.isEmpty()) {
                childType.setChildren(grandChildren);
            }

            childrenList.add(childType);
        });

        return childrenList;
    }

    /**
     * 获取诉求列表
     * 
     * @param appealListDTO 诉求列表查询参数
     * @return 诉求列表响应对象
     * @throws Exception 处理过程中的异常
     */
    @Override
    public AppealListRespVO getAppealList(AppealListDTO appealListDTO) throws Exception {
        AppealListRespVO appealListRespVO = new AppealListRespVO();
        appealListRespVO.setCurrentPage(appealListDTO.getCurrentPage());
        appealListRespVO.setPageSize(appealListDTO.getPageSize());

        // 获取总条数
        BuilderSql countSql = getCountBuilderSql(appealListDTO);
        List<Map<String, Object>> countResult = ConvertDataUtils
                .convertListMapKeyToLowerCase(dataSqlService.ebuilderFromSql(countSql.getSql(),
                        appealListDTO.getCurrentPage(), appealListDTO.getPageSize(), SourceType.LOGIC,
                        countSql.getParams()));
        if (countResult.isEmpty()) {
            appealListRespVO.setTotal(0);
            return appealListRespVO;
        }

        appealListRespVO.setTotal(Integer.parseInt(countResult.get(0).get("total").toString()));

        // 获取数据
        BuilderSql builderSql = getBuilderSql(appealListDTO);
        List<Map<String, Object>> rawData = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.ebuilderFromSql(builderSql.getSql(), appealListDTO.getCurrentPage(),
                        appealListDTO.getPageSize(), SourceType.LOGIC, builderSql.getParams()));

        // 处理结果数据
        List<Map<String, Object>> processedData = processRawData(rawData);

        appealListRespVO.setData(processedData);
        return appealListRespVO;
    }

    @Override
    public Map<String, Object> getAppealDetail(String no) {
        if (StringUtils.isEmpty(no)) {
            return null;
        }

        // 查询诉求基本信息
        Map<String, Object> detailData = queryAppealBasicInfo(no);
        if (detailData == null || detailData.isEmpty()) {
            return null;
        }

        // 获取分类名称映射
        Map<String, String> typeMap = getTypeMap();

        // 处理诉求详情数据
        processAppealDetailData(detailData, typeMap);

        // 说明图片处理
        String shuoMtp = StringUtils.null2String(MapUtil.getStr(detailData, "shuo_mtp"));
        List<Map<String, Object>> imageList = getImageList(shuoMtp);
        detailData.put("shuo_mtp", imageList);

        int liu_ll = MapUtil.getInt(detailData, "liu_ll", 0);

        // 浏览量+1
        updateBrowseCount(no, liu_ll);

        return detailData;
    }

    @Override
    public Boolean addAppealNoHelp(AppealHelpDTO appealHelpDTO) {
        String bian_h = appealHelpDTO.getBian_h();// 诉求编号
        String title = appealHelpDTO.getTitle();// 反馈标题
        String content = appealHelpDTO.getContent();// 反馈原因

        SimpleEmployee currentUser = UserContext.getCurrentUser();
        UserInfoResult userInfoResult = openPlatformService.getUser(currentUser.getUid());
        Long deptId = userInfoResult.getDepartment().get(0);// 部门ID
        String mobile = userInfoResult.getMobile();// 联系方式
        String formId = dataBaseService.getBaseValue("uf_mei_bzfkjl", "objId");

        // 插入表单
        LocalDateTime now = LocalDateTime.now();
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        data.put("fan_kr", currentUser.getUid());
        data.put("bu_m", deptId);
        data.put("lian_xfs", mobile);
        data.put("bian_h", bian_h);
        data.put("biao_t", title);
        data.put("fan_kyy", content);
        data.put("fan_ksj", now.format(DATE_TIME_FORMATTER));
        data.put("formmodeid", formId);
        data.put("create_time", now.format(DATE_TIME_FORMATTER));
        dataList.add(Collections.singletonMap("mainTable", data));

        // 插入表单
        EBuilderUtil.saveFormDataV2(cmicProperties.getOpenPlatformUrl(), currentUser.getUid(), formId, dataList,
                openPlatformService.getAccessToken());

        // 重构建模表中用户的权限

        // 浏览量+1
        String wtSql = "select * from uf_wen_tkwh where bian_h = ?";
        List<Map<String, Object>> wtResult = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(wtSql, SourceType.LOGIC, Collections.singletonList(bian_h)));

        if (wtResult.isEmpty()) {
            log.warn("问题库数据查询为空! 诉求编号: {} 不存在", bian_h);
            return true;
        }

        String wenTkwhFormId = dataBaseService.getBaseValue("uf_wen_tkwh", "objId");

        wtResult.forEach(item -> {
            int mei_bzs = MapUtil.getInt(item, "mei_bzs", 0);
            Map<String, Object> mainTable = new HashMap<>();
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("mei_bzs", mei_bzs + 1);
            updateData.put("bian_h", bian_h);
            mainTable.put("mainTable", updateData);

            Map<String, Object> updateFields = new HashMap<>();
            List<String> fields = new ArrayList<>();
            fields.add("bian_h");
            updateFields.put("mainTable", fields);

            EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder()
                    .userId(currentUser.getUid().toString())
                    .objId(wenTkwhFormId)
                    .updateType("updatePolicy")
                    .datas(Collections.singletonList(mainTable))
                    .updateField(updateFields)
                    .build();
            log.info("更新没帮助次数请求: {}", ebFormDataReq);
            EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(),
                    cmicProperties.getOpenPlatformUrl());

        });

        return Boolean.TRUE;
    }

    @Override
    public Boolean addAppealHelp(AppealHelpDTO appealHelpDTO) {
        String bian_h = appealHelpDTO.getBian_h();// 诉求编号
        String wtSql = "select * from uf_wen_tkwh where bian_h = ?";
        List<Map<String, Object>> wtResult = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(wtSql, SourceType.LOGIC, Collections.singletonList(bian_h)));

        if (wtResult.isEmpty()) {
            log.warn("问题库数据查询为空! 诉求编号: {} 不存在", bian_h);
            return true;
        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        wtResult.forEach(item -> {
            int you_bzs = MapUtil.getInt(item, "you_bzs", 0);
            Map<String, Object> mainTable = new HashMap<>();
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("you_bzs", you_bzs + 1);
            updateData.put("bian_h", bian_h);
            mainTable.put("mainTable", updateData);
            dataList.add(mainTable);
        });
        Map<String, Object> updateFields = new HashMap<>();
        List<String> fields = new ArrayList<>();
        fields.add("bian_h");
        updateFields.put("mainTable", fields);

        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String formId = dataBaseService.getBaseValue("uf_wen_tkwh", "objId");
        EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder()
                .userId(currentUser.getUid().toString())
                .objId(formId)
                .updateType("updatePolicy")
                .datas(dataList)
                .updateField(updateFields)
                .build();

        EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(),
                cmicProperties.getOpenPlatformUrl());
        return Boolean.TRUE;
    }

    @Override
    public AppealListRespVO getHotAppealList(Integer currentPage, Integer pageSize) throws Exception {
        AppealListRespVO appealListRespVO = new AppealListRespVO();
        appealListRespVO.setCurrentPage(currentPage);
        appealListRespVO.setPageSize(pageSize);

        String countSql = "select count(1) as total from uf_wen_tkwh t where t.zhuang_t = 0 and t.shi_frmwt = 0";
        List<Map<String, Object>> countResult = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(countSql, SourceType.LOGIC));
        if (countResult.isEmpty()) {
            appealListRespVO.setTotal(0);
            return appealListRespVO;
        }
        Integer total = Integer.parseInt(countResult.get(0).get("total").toString());

        // 获取数据
        String sql = "select t.* from uf_wen_tkwh t where t.zhuang_t = 0 and t.shi_frmwt = 0";
        List<Map<String, Object>> result = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.ebuilderFromSql(sql, currentPage, pageSize, SourceType.LOGIC));

        // 处理数据
        List<Map<String, Object>> processedData = processRawData(result);
        appealListRespVO.setTotal(total);
        appealListRespVO.setData(processedData);
        return appealListRespVO;
    }

    @Override
    public Boolean setHot(String ids, String hot) {
        List<String> idList = Arrays.asList(ids.split(","));
        List<Map<String, Object>> dataList = new ArrayList<>();
        idList.forEach(id -> {
            Map<String, Object> mainTable = new HashMap<>();
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("id", id);
            updateData.put("shi_frmwt", hot);
            mainTable.put("mainTable", updateData);
            dataList.add(mainTable);
        });

        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String formId = dataBaseService.getBaseValue("uf_wen_tkwh", "objId");
        EbFormDataReq ebFormDataReq = new EbFormDataReq.Builder()
                .userId(currentUser.getUid().toString())
                .objId(formId)
                .datas(dataList)
                .build();

        EBuilderUtil.updateFormDataV2(ebFormDataReq, openPlatformService.getAccessToken(),
                cmicProperties.getOpenPlatformUrl());
        return Boolean.TRUE;
    }

    /**
     * 查询诉求基本信息
     * 
     * @param no 诉求编号
     * @return 诉求基本信息
     */
    private Map<String, Object> queryAppealBasicInfo(String no) {
        String sql = "SELECT * FROM uf_wen_tkwh WHERE zhuang_t = 0 AND bian_h = ?";
        List<Map<String, Object>> result = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC, Collections.singletonList(no)));

        return result.isEmpty() ? null : result.get(0);
    }

    /**
     * 处理诉求详情数据
     * 
     * @param detailData 诉求基本信息
     * @param typeMap    分类名称映射
     */
    private void processAppealDetailData(Map<String, Object> detailData, Map<String, String> typeMap) {
        // 获取基本字段
        String bianHao = StringUtils.null2String(MapUtil.getStr(detailData, "bian_h"));
        String biaoTi = StringUtils.null2String(MapUtil.getStr(detailData, "biao_t"));
        String guiSfl = StringUtils.null2String(MapUtil.getStr(detailData, "gui_sfl"));
        int liuLl = MapUtil.getInt(detailData, "liu_ll", 0);
        String zhengW = StringUtils.null2String(MapUtil.getStr(detailData, "zheng_w"));
        String xiangGfw = StringUtils.null2String(MapUtil.getStr(detailData, "xiang_gfw"));

        // 处理分类名称
        String categoryId = guiSfl.contains("_") ? guiSfl.split("_")[1] : guiSfl;

        // 构建结果数据
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("bian_h", bianHao);
        resultData.put("biao_t", biaoTi);
        resultData.put("gui_sfl", typeMap.getOrDefault(categoryId, ""));
        resultData.put("liu_ll", liuLl + 1);
        resultData.put("zheng_w", zhengW);
        resultData.put("xiang_gfw", xiangGfw);

        // 处理相关服务
        processRelatedServices(resultData, xiangGfw);

        // 处理附件
        String xiangGfj = StringUtils.null2String(MapUtil.getStr(detailData, "xiang_gfj"));
        resultData.put("xiang_gfj", processAttachments(xiangGfj));

        // 更新原始数据Map
        detailData.clear();
        detailData.putAll(resultData);
    }

    /**
     * 查询并合并相关服务信息到结果数据中
     * 
     * @param resultData 结果数据
     * @param serviceIds 相关服务ID列表
     */
    private void mergeRelatedServices(List<Map<String, Object>> resultData, List<String> serviceIds) {
        // 查询相关服务
        Map<String, Map<String, Object>> serviceMap = getXgfwMap(serviceIds);

        // 合并服务数据到结果中
        resultData.forEach(data -> {
            String serviceIdsStr = StringUtils.null2String(MapUtil.getStr(data, "xiang_gfw"));
            data.put("xgfwList", buildServiceList(serviceIdsStr, serviceMap));
        });
    }

    /**
     * 处理相关服务信息（单条数据）
     * 
     * @param resultData    结果数据
     * @param serviceIdsStr 服务ID字符串，多个ID以逗号分隔
     */
    private void processRelatedServices(Map<String, Object> resultData, String serviceIdsStr) {
        List<String> serviceIds = Arrays.stream(serviceIdsStr.split(","))
                .filter(StringUtils::isNotBlank)
                .collect(java.util.stream.Collectors.toList());

        Map<String, Map<String, Object>> serviceMap = getXgfwMap(serviceIds);
        resultData.put("xgfwList", buildServiceList(serviceIdsStr, serviceMap));
    }

    /**
     * 构建服务信息列表
     * 
     * @param serviceIdsStr 服务ID字符串，多个ID以逗号分隔
     * @param serviceMap    服务信息映射
     * @return 服务信息列表
     */
    private List<Map<String, Object>> buildServiceList(String serviceIdsStr,
            Map<String, Map<String, Object>> serviceMap) {
        return Arrays.stream(serviceIdsStr.split(","))
                .filter(StringUtils::isNotBlank)
                .map(serviceId -> {
                    Map<String, Object> serviceInfo = serviceMap.get(serviceId);
                    if (serviceInfo != null) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("fu_wmc", MapUtil.getStr(serviceInfo, "fu_wmc"));
                        item.put("zhi_xljdz", MapUtil.getStr(serviceInfo, "zhi_xljdz"));
                        item.put("can_s", MapUtil.getStr(serviceInfo, "can_s"));
                        item.put("ren_zfs", MapUtil.getStr(serviceInfo, "ren_zfs"));
                        item.put("da_kfs", MapUtil.getStr(serviceInfo, "da_kfs"));
                        item.put("liu_clx", MapUtil.getStr(serviceInfo, "liu_clx"));
                        return item;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取分类名称映射
     * 
     * @return 分类ID和名称的映射
     */
    private Map<String, String> getTypeMap() {
        String sql = "SELECT id, fen_lmc FROM uf_fen_lwh";
        List<Map<String, Object>> result = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC));

        return result.stream()
                .collect(java.util.stream.Collectors.toMap(
                        item -> MapUtil.getStr(item, "id"),
                        item -> StringUtils.null2String(MapUtil.getStr(item, "fen_lmc")),
                        (v1, v2) -> v1));
    }

    /**
     * 处理原始查询数据，包括附件处理和相关服务信息合并
     * 
     * @param rawData 原始查询数据
     * @return 处理后的数据
     */
    private List<Map<String, Object>> processRawData(List<Map<String, Object>> rawData) {
        if (rawData.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集所有相关服务ID
        List<String> serviceIds = collectServiceIds(rawData);

        // 转换为结果数据
        List<Map<String, Object>> resultData = rawData.stream().map(item -> {
            Map<String, Object> data = new HashMap<>();
            data.put("biao_t", StringUtils.null2String(MapUtil.getStr(item, "biao_t")));
            data.put("zheng_w", StringUtils.null2String(MapUtil.getStr(item, "zheng_w")));
            data.put("bian_h", StringUtils.null2String(MapUtil.getStr(item, "bian_h")));
            data.put("xiang_gfw", StringUtils.null2String(MapUtil.getStr(item, "xiang_gfw")));

            // 处理附件
            data.put("xiang_gfj", processAttachments(MapUtil.getStr(item, "xiang_gfj")));

            return data;
        }).collect(java.util.stream.Collectors.toList());

        // 查询并合并相关服务信息
        mergeRelatedServices(resultData, serviceIds);

        return resultData;
    }

    /**
     * 从原始数据中收集所有相关服务ID
     * 
     * @param rawData 原始数据
     * @return 相关服务ID列表
     */
    private List<String> collectServiceIds(List<Map<String, Object>> rawData) {
        return rawData.stream()
                .map(item -> StringUtils.null2String(MapUtil.getStr(item, "xiang_gfw")))
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 处理附件信息
     * 
     * @param attachmentIds 附件ID字符串，多个ID以逗号分隔
     * @return 附件信息列表
     */
    private List<Map<String, Object>> processAttachments(String attachmentIds) {
        List<Map<String, Object>> fileList = new ArrayList<>();
        if (StringUtils.isBlank(attachmentIds)) {
            return fileList;
        }

        Arrays.stream(attachmentIds.split(","))
                .filter(StringUtils::isNotBlank)
                .forEach(fileId -> {
                    String docSql = "SELECT t1.name AS docsubject FROM document t1 WHERE t1.doc_file = ? " +
                            "AND t1.delete_type = 0 AND t1.tenant_key = ?";
                    List<Map<String, Object>> docResult = ConvertDataUtils
                            .convertListMapKeyToLowerCase(dataSqlService.executeCommonSqlAll(docSql,
                                    SourceType.LOGIC, DsLogicGroupIdEnum.DOCUMENT.getGroupId(),
                                    Arrays.asList(fileId, cmicProperties.getHostTenantKey())));

                    docResult.stream().findFirst().ifPresent(doc -> {
                        Map<String, Object> file = new HashMap<>();
                        file.put("name", StringUtils.null2String(MapUtil.getStr(doc, "docsubject")));
                        file.put("id", fileId);
                        fileList.add(file);
                    });
                });

        return fileList;
    }

    /**
     * 获取相关服务映射
     *
     * @param xgfwList 相关服务ID列表
     * @return 相关服务映射
     */
    private Map<String, Map<String, Object>> getXgfwMap(List<String> xgfwList) {
        // 查询相关服务
        Map<String, Map<String, Object>> xgfwMap = new HashMap<>();
        if (xgfwList.isEmpty()) {
            return xgfwMap;
        }

        // 去重并过滤空值
        List<String> distinctIds = xgfwList.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(java.util.stream.Collectors.toList());

        if (distinctIds.isEmpty()) {
            return xgfwMap;
        }

        // 构建IN条件参数
        String idPlaceholders = distinctIds.stream().map(id -> "?").collect(Collectors.joining(","));
        String xgfwSql = "select id,fu_wmc,zhi_xljdz,can_s,ren_zfs,da_kfs,liu_clx from uf_fu_wxxwh where id in ("
                + idPlaceholders + ")";

        List<Map<String, Object>> result = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.executeCommonSqlAll(xgfwSql, SourceType.LOGIC,
                        DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId(), distinctIds));

        result.forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("fu_wmc", StringUtils.null2String(MapUtil.getStr(item, "fu_wmc")));
            map.put("zhi_xljdz", StringUtils.null2String(MapUtil.getStr(item, "zhi_xljdz")));
            map.put("can_s", StringUtils.null2String(MapUtil.getStr(item, "can_s")));
            map.put("ren_zfs", StringUtils.null2String(MapUtil.getStr(item, "ren_zfs")));
            map.put("da_kfs", StringUtils.null2String(MapUtil.getStr(item, "da_kfs")));
            map.put("liu_clx", StringUtils.null2String(MapUtil.getStr(item, "liu_clx")));
            xgfwMap.put(MapUtil.getStr(item, "id"), map);
        });

        return xgfwMap;
    }

    private BuilderSql getCountBuilderSql(AppealListDTO appealListDTO) {
        BuilderSql builderSql = new BuilderSql();
        StringBuilder sql = new StringBuilder("select count(1) as total from uf_wen_tkwh t where t.zhuang_t = 0");
        List<String> params = new ArrayList<>();
        if (StringUtils.isNotEmpty(appealListDTO.getType())) {
            String type = this.getTreeId(appealListDTO.getType());// 获取树id
            sql.append(" and t.gui_sfl = ?");
            params.add(type);
        }
        if (StringUtils.isNotEmpty(appealListDTO.getKeyword())) {
            sql.append(" and (t.biao_t like ? or t.zheng_w like ?)");
            params.add("%" + appealListDTO.getKeyword() + "%");
            params.add("%" + appealListDTO.getKeyword() + "%");
        }
        if (StringUtils.isNotEmpty(appealListDTO.getShiFrmwt())) {
            sql.append(" and t.shi_frmwt = ?");
            params.add(appealListDTO.getShiFrmwt());
        }
        builderSql.setSql(sql.toString());
        builderSql.setParams(params);
        return builderSql;
    }

    private BuilderSql getBuilderSql(AppealListDTO appealListDTO) {
        BuilderSql builderSql = new BuilderSql();
        StringBuilder sql = new StringBuilder("select t.* from uf_wen_tkwh t where t.zhuang_t = 0");
        List<String> params = new ArrayList<>();
        if (StringUtils.isNotEmpty(appealListDTO.getType())) {
            String type = this.getTreeId(appealListDTO.getType());// 获取树id
            sql.append(" and t.gui_sfl = ?");
            params.add(type);
        }
        if (StringUtils.isNotEmpty(appealListDTO.getKeyword())) {
            sql.append(" and (t.biao_t like ? or t.zheng_w like ?)");
            params.add("%" + appealListDTO.getKeyword() + "%");
            params.add("%" + appealListDTO.getKeyword() + "%");
        }
        if (StringUtils.isNotEmpty(appealListDTO.getShiFrmwt())) {
            sql.append(" and t.shi_frmwt = ?");
            params.add(appealListDTO.getShiFrmwt());
        }
        sql.append(
                " order by case when t.update_time is null then t.chuang_jsj else t.update_time end desc ");
        builderSql.setSql(sql.toString());
        builderSql.setParams(params);
        return builderSql;
    }

    // 获取树id
    private String getTreeId(String id) {
        String treeId = dataBaseService.getBaseDataValue("分类树id", "诉求中心");
        return treeId + id;
    }

    /**
     * 获取多个图片
     *
     * @param docIds 文件IDs，多个ID以逗号分隔
     * @return 图片信息列表
     */
    private List<Map<String, Object>> getImageList(String docIds) {
        List<Map<String, Object>> imageList = new ArrayList<>();
        if (StringUtils.isBlank(docIds)) {
            return imageList;
        }

        Arrays.stream(docIds.split(","))
                .filter(StringUtils::isNotBlank)
                .forEach(docId -> {
                    Map<String, Object> image = getImage(docId);
                    if (!image.isEmpty()) {
                        imageList.add(image);
                    }
                });

        return imageList;
    }

    /**
     * 获取单个图片信息
     *
     * @param docId 文件ID
     * @return 图片信息
     */
    private Map<String, Object> getImage(String docId) {
        Map<String, Object> imageData = new HashMap<>();
        if (StringUtils.isBlank(docId)) {
            return imageData;
        }

        String sql = "select doc.name, access.file_id from document_main_access access join document doc " +
                "on access.file_id = doc.doc_file where access.delete_type = 0 and access.doc_id = ? " +
                "and access.tenant_key = ?";
        List<Map<String, Object>> result = ConvertDataUtils.convertListMapKeyToLowerCase(
                dataSqlService.eBuilderFromSqlAll(sql, SourceType.LOGIC,
                        Arrays.asList(docId, cmicProperties.getHostTenantKey())));

        result.stream().findFirst().ifPresent(item -> {
            String imageFileId = StringUtils.null2String(MapUtil.getStr(item, "file_id"));
            String imageFileName = StringUtils.null2String(MapUtil.getStr(item, "name"));

            if (StringUtils.isNotBlank(imageFileId)) {
                String imageUrl = "/api/file/v2/common/download/" + imageFileId;
                imageData.put("url", imageUrl);
                imageData.put("name", imageFileName);
            }
        });

        return imageData;
    }

    /**
     * 更新诉求浏览次数
     * 
     * @param no     诉求编号
     * @param liu_ll 浏览次数
     */
    private void updateBrowseCount(String no, int liu_ll) {
        // 获取ObjId
        String objId = dataBaseService.getBaseValue("uf_wen_tkwh", "objId");
        log.info("获取到的人员信息表ObjId: {}", objId);

        // 构建更新请求
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> mainTable = new HashMap<>();
        Map<String, Object> updateData = new HashMap<>();
        updateData.put("liu_ll", liu_ll + 1);
        updateData.put("bian_h", no);
        mainTable.put("mainTable", updateData);
        dataList.add(mainTable);

        Map<String, Object> updateFields = new HashMap<>();
        List<String> mainTableUpdateFields = new ArrayList<>();
        mainTableUpdateFields.add("liu_ll");
        updateFields.put("mainTable", mainTableUpdateFields);

        // 使用API调用更新数据
        SimpleEmployee currentUser = UserContext.getCurrentUser();
        String userId = String.valueOf(currentUser.getUid());

        // 构建请求对象
        EbFormDataReq.Builder builder = new EbFormDataReq.Builder()
                .userId(userId)
                .objId(objId)
                .updateType("updatePolicy")
                .updateField(updateFields)
                .datas(dataList);

        EbFormDataReq ebFormDataReq = builder.build();
        // 发送请求
        log.info("更新诉求浏览次数请求: {}", ebFormDataReq);
        EBuilderUtil.updateFormDataV2(ebFormDataReq,
                openPlatformService.getAccessToken(),
                cmicProperties.getOpenPlatformUrl());
    }

    @Data
    public static class BuilderSql {
        private String sql;
        private List<String> params;
    }

}