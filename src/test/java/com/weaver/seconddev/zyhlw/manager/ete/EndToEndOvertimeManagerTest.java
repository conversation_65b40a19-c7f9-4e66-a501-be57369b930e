package com.weaver.seconddev.zyhlw.manager.ete;

import com.weaver.common.base.entity.result.WeaResult;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.supervise.OperatorInfo;
import com.weaver.seconddev.zyhlw.domain.supervise.OvertimeConfig;
import com.weaver.seconddev.zyhlw.manager.supervise.SuperviseMessageManager;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 端对端督办任务公共管理器单元测试
 *
 * <AUTHOR>
 * @date 2025/6/23 14:30
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("端对端督办任务公共管理器测试")
class EndToEndOvertimeManagerTest {

    @Mock
    private IDataBaseService dataBaseService;

    @Mock
    private IDataSqlService dataSqlService;

    @Mock
    private SuperviseMessageManager superviseMessageManager;

    @Mock
    private CmicProperties cmicProperties;

    @InjectMocks
    private EndToEndOvertimeManager endToEndOvertimeManager;

    private static final String TEST_TABLE_NAME = "test_table";
    private static final String TEST_REQUEST_ID = "12345";
    private static final String TEST_USER_ID = "67890";
    private static final String TEST_SELECT_NODE_ID = "1,2,3";
    private static final String TEST_TENANT_KEY = "tld0nhuikk";
    private static final String TEST_CLASS_NAME = "TestAction";
    private static final Long TEST_SEND_USER_ID = 1L;

    @BeforeEach
    void setUp() {
        // 不在 setUp 中设置通用的 stub，而是在需要的测试用例中设置
    }

    @Test
    @DisplayName("正常执行协办任务超时提醒")
    void testExecuteOvertimeTask_CoOperation_Normal() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        Map<String, Object> workflowData = createWorkflowData();
        List<Map<String, Object>> dataResult = Arrays.asList(workflowData);

        OperatorInfo operatorInfo = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> currentOperatorMap = createEmployeeMap("operator001", "13800138001");
        Map<String, Object> creatorMap = createEmployeeMap("creator001", "13800138002");
        Map<String, Object> lastOperatorMap = createEmployeeMap("lastOperator001", "13800138003");

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "5");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 1);

        List<Map<String, Object>> saveDataList = Arrays.asList(new HashMap<>());

        // Mock 方法调用
        when(cmicProperties.getHostTenantKey()).thenReturn(TEST_TENANT_KEY);
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo);
        when(dataBaseService.getEmployeeInfo("operator001")).thenReturn(currentOperatorMap);
        when(dataBaseService.getEmployeeInfo("creator001")).thenReturn(creatorMap);
        when(dataBaseService.getEmployeeInfo("lastOperator001")).thenReturn(lastOperatorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap);
        when(superviseMessageManager.buildMessage(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString())).thenReturn(saveDataList);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证方法调用
        verify(dataBaseService).getBaseDataValue("协办任务下达流程表", "端对端服务责任管理");
        verify(superviseMessageManager).getCurrentOperatorInfo("node001", TEST_REQUEST_ID);
        verify(superviseMessageManager).buildMessage(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), eq("服务责任任务到期提醒(协办)"), anyString(), anyString(),
                anyString(), anyString(), anyString());
        verify(superviseMessageManager).saveMessage(saveDataList, TEST_REQUEST_ID, "operator001",
                "node001", "测试流程", "0", TEST_SEND_USER_ID);
    }

    @Test
    @DisplayName("正常执行主办任务超时提醒")
    void testExecuteOvertimeTask_MainOperation_Normal() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("10")
                .warnMaxNum("1")
                .build();

        Map<String, Object> workflowData = createWorkflowData();
        List<Map<String, Object>> dataResult = Arrays.asList(workflowData);

        OperatorInfo operatorInfo = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> currentOperatorMap = createEmployeeMap("operator001", "13800138001");
        Map<String, Object> creatorMap = createEmployeeMap("creator001", "13800138002");
        Map<String, Object> lastOperatorMap = createEmployeeMap("lastOperator001", "13800138003");

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "10");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 0);

        List<Map<String, Object>> saveDataList = Arrays.asList(new HashMap<>());

        // Mock 方法调用
        when(cmicProperties.getHostTenantKey()).thenReturn(TEST_TENANT_KEY);
        when(dataBaseService.getBaseDataValue("主办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo);
        when(dataBaseService.getEmployeeInfo("operator001")).thenReturn(currentOperatorMap);
        when(dataBaseService.getEmployeeInfo("creator001")).thenReturn(creatorMap);
        when(dataBaseService.getEmployeeInfo("lastOperator001")).thenReturn(lastOperatorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap);
        when(superviseMessageManager.buildMessage(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString())).thenReturn(saveDataList);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "主办", "主办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证方法调用
        verify(dataBaseService).getBaseDataValue("主办任务下达流程表", "端对端服务责任管理");
        verify(superviseMessageManager).buildMessage(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), eq("服务责任任务到期提醒(主办)"), anyString(), anyString(),
                anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("用户ID为空时返回失败")
    void testExecuteOvertimeTask_UserIdNull() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertFalse(result.isStatus());
        assertEquals(TEST_CLASS_NAME + ": 用户id不能为空", result.getMsg());
    }

    @Test
    @DisplayName("超时配置为空时使用默认值")
    void testExecuteOvertimeTask_OvertimeConfigNull() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(null);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertFalse(result.isStatus());
        assertEquals(TEST_CLASS_NAME + ":未查询到需要督办的数据", result.getMsg());

        // 验证使用默认值调用SQL（afterDayNum = "10"）
        verify(dataSqlService).executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), eq(Collections.singletonList("10")));
    }

    @Test
    @DisplayName("数据结果为空时返回失败")
    void testExecuteOvertimeTask_EmptyDataResult() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertFalse(result.isStatus());
        assertEquals(TEST_CLASS_NAME + ":未查询到需要督办的数据", result.getMsg());
    }

    /**
     * 创建工作流数据
     */
    private Map<String, Object> createWorkflowData() {
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTID", TEST_REQUEST_ID);
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("LASTOPERATOR", "lastOperator001");
        workflowData.put("CREATER", "creator001");
        workflowData.put("CREATEDATETIME", "1640995200000");
        workflowData.put("WORKFLOWID", "workflow001");
        return workflowData;
    }

    @Test
    @DisplayName("已达到最大提醒次数时不发送消息")
    void testExecuteOvertimeTask_MaxRemindTimesReached() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("1")
                .build();

        Map<String, Object> workflowData = createWorkflowData();
        List<Map<String, Object>> dataResult = Arrays.asList(workflowData);

        OperatorInfo operatorInfo = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> currentOperatorMap = createEmployeeMap("operator001", "13800138001");
        Map<String, Object> creatorMap = createEmployeeMap("creator001", "13800138002");
        Map<String, Object> lastOperatorMap = createEmployeeMap("lastOperator001", "13800138003");

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "5");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 2); // 已达到最大提醒次数

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo);
        when(dataBaseService.getEmployeeInfo("operator001")).thenReturn(currentOperatorMap);
        when(dataBaseService.getEmployeeInfo("creator001")).thenReturn(creatorMap);
        when(dataBaseService.getEmployeeInfo("lastOperator001")).thenReturn(lastOperatorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证不会调用消息构建和保存方法
        verify(superviseMessageManager, never()).buildMessage(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString());
        verify(superviseMessageManager, never()).saveMessage(any(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("接收人为空时不发送消息")
    void testExecuteOvertimeTask_EmptyReceiver() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        Map<String, Object> workflowData = createWorkflowData();
        List<Map<String, Object>> dataResult = Arrays.asList(workflowData);

        OperatorInfo operatorInfo = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> currentOperatorMap = createEmployeeMap("", "13800138001"); // 接收人为空
        Map<String, Object> creatorMap = createEmployeeMap("creator001", "13800138002");
        Map<String, Object> lastOperatorMap = createEmployeeMap("lastOperator001", "13800138003");

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "5");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 1);

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo);
        when(dataBaseService.getEmployeeInfo("operator001")).thenReturn(currentOperatorMap);
        when(dataBaseService.getEmployeeInfo("creator001")).thenReturn(creatorMap);
        when(dataBaseService.getEmployeeInfo("lastOperator001")).thenReturn(lastOperatorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证不会调用消息构建和保存方法
        verify(superviseMessageManager, never()).buildMessage(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString());
        verify(superviseMessageManager, never()).saveMessage(any(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试SQL构建包含环节ID")
    void testGetWorkflowInfoSql_WithSelectNodeId() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        endToEndOvertimeManager.executeOvertimeTask(params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证SQL包含环节ID条件
        verify(dataSqlService).executeCommonSqlAll(argThat(sql ->
                sql.contains("AND workflow_requestbase.nodeid IN (" + TEST_SELECT_NODE_ID + ")")),
                eq(SourceType.LOGIC), eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList());
    }

    @Test
    @DisplayName("测试SQL构建不包含环节ID")
    void testGetWorkflowInfoSql_WithoutSelectNodeId() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId("")
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        endToEndOvertimeManager.executeOvertimeTask(params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证SQL不包含环节ID条件
        verify(dataSqlService).executeCommonSqlAll(argThat(sql ->
                !sql.contains("AND workflow_requestbase.nodeid IN")),
                eq(SourceType.LOGIC), eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList());
    }

    @Test
    @DisplayName("测试异常处理_数据库服务异常")
    void testExecuteOvertimeTask_DatabaseServiceException() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        // Mock 异常
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试并验证异常
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            endToEndOvertimeManager.executeOvertimeTask(params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);
        });

        assertEquals("数据库连接失败", thrownException.getMessage());
    }

    @Test
    @DisplayName("测试异常处理_SQL执行异常")
    void testExecuteOvertimeTask_SqlExecutionException() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenThrow(new RuntimeException("SQL执行失败"));

        // 执行测试并验证异常
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            endToEndOvertimeManager.executeOvertimeTask(params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);
        });

        assertEquals("SQL执行失败", thrownException.getMessage());
    }

    @Test
    @DisplayName("测试多条数据处理")
    void testExecuteOvertimeTask_MultipleDataRecords() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        Map<String, Object> workflowData1 = createWorkflowData();
        Map<String, Object> workflowData2 = createWorkflowData();
        workflowData2.put("REQUESTID", "67890");
        workflowData2.put("CURRENTNODEID", "node002");
        workflowData2.put("REQUESTNAME", "测试流程2");

        List<Map<String, Object>> dataResult = Arrays.asList(workflowData1, workflowData2);

        OperatorInfo operatorInfo1 = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        OperatorInfo operatorInfo2 = OperatorInfo.builder()
                .userId("operator002")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> currentOperatorMap1 = createEmployeeMap("operator001", "13800138001");
        Map<String, Object> currentOperatorMap2 = createEmployeeMap("operator002", "13800138002");
        Map<String, Object> creatorMap = createEmployeeMap("creator001", "13800138003");
        Map<String, Object> lastOperatorMap = createEmployeeMap("lastOperator001", "13800138004");

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "5");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 1);

        List<Map<String, Object>> saveDataList = Arrays.asList(new HashMap<>());

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo1);
        when(superviseMessageManager.getCurrentOperatorInfo("node002", "67890"))
                .thenReturn(operatorInfo2);
        when(dataBaseService.getEmployeeInfo("operator001")).thenReturn(currentOperatorMap1);
        when(dataBaseService.getEmployeeInfo("operator002")).thenReturn(currentOperatorMap2);
        when(dataBaseService.getEmployeeInfo("creator001")).thenReturn(creatorMap);
        when(dataBaseService.getEmployeeInfo("lastOperator001")).thenReturn(lastOperatorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap)
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap)
                .thenReturn(timesDataMap);
        when(superviseMessageManager.buildMessage(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString())).thenReturn(saveDataList);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证每条数据都被处理
        verify(superviseMessageManager).getCurrentOperatorInfo("node001", TEST_REQUEST_ID);
        verify(superviseMessageManager).getCurrentOperatorInfo("node002", "67890");
        verify(superviseMessageManager, times(2)).buildMessage(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString());
        verify(superviseMessageManager, times(2)).saveMessage(any(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试员工信息为空的情况")
    void testExecuteOvertimeTask_EmptyEmployeeInfo() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("userId", TEST_SEND_USER_ID);

        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .afterDayNum("5")
                .warnMaxNum("2")
                .build();

        Map<String, Object> workflowData = createWorkflowData();
        List<Map<String, Object>> dataResult = Arrays.asList(workflowData);

        OperatorInfo operatorInfo = OperatorInfo.builder()
                .userId("operator001")
                .receiveDateTime("1640995200000")
                .build();

        Map<String, Object> emptyEmployeeMap = new HashMap<>(); // 空的员工信息

        Map<String, Object> supervisorMap = new HashMap<>();
        supervisorMap.put("YAO_QWCRQ", "2024-12-31");
        supervisorMap.put("DAYS", "5");

        Map<String, Object> timesDataMap = new HashMap<>();
        timesDataMap.put("TIMES", 1);

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("协办任务下达流程表", "端对端服务责任管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(dataResult);
        when(superviseMessageManager.getCurrentOperatorInfo("node001", TEST_REQUEST_ID))
                .thenReturn(operatorInfo);
        when(dataBaseService.getEmployeeInfo(anyString())).thenReturn(emptyEmployeeMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(supervisorMap);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(timesDataMap);

        // 执行测试
        WeaResult<Map<String, Object>> result = endToEndOvertimeManager.executeOvertimeTask(
                params, "协办", "协办任务下达流程表", TEST_CLASS_NAME);

        // 验证结果
        assertTrue(result.isStatus());
        assertTrue((Boolean) result.getData().get("success"));

        // 验证不会调用消息构建和保存方法（因为接收人为空）
        verify(superviseMessageManager, never()).buildMessage(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString());
        verify(superviseMessageManager, never()).saveMessage(any(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyLong());
    }

    /**
     * 创建员工信息数据
     */
    private Map<String, Object> createEmployeeMap(String loginValue, String mobile) {
        Map<String, Object> employeeMap = new HashMap<>();
        employeeMap.put("LOGIN_VALUE", loginValue);
        employeeMap.put("MOBILE", mobile);
        return employeeMap;
    }
}
