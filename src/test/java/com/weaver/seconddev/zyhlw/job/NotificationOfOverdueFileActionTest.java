package com.weaver.seconddev.zyhlw.job;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.domain.supervise.OvertimeConfig;
import com.weaver.seconddev.zyhlw.manager.sms.SmsRemindManager;
import com.weaver.seconddev.zyhlw.manager.supervise.SuperviseMessageManager;
import com.weaver.seconddev.zyhlw.service.IDataBaseService;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import com.weaver.seconddev.zyhlw.util.CmicProperties;
import com.weaver.seconddev.zyhlw.util.enums.DsLogicGroupIdEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 逾期档案归档通知单流程Job单元测试
 *
 * <AUTHOR>
 * @date 2025/01/25
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("逾期档案归档通知单流程Job测试")
class NotificationOfOverdueFileActionTest {

    @Mock
    private IDataBaseService dataBaseService;

    @Mock
    private IDataSqlService dataSqlService;

    @Mock
    private SuperviseMessageManager superviseMessageManager;

    @Mock
    private SmsRemindManager smsRemindManager;

    @Mock
    private CmicProperties cmicProperties;

    @InjectMocks
    private NotificationOfOverdueFileAction notificationOfOverdueFileAction;

    private static final String TEST_TABLE_NAME = "test_table";
    private static final String TEST_REQUEST_ID = "12345";
    private static final String TEST_USER_ID = "67890";
    private static final String TEST_SELECT_NODE_ID = "1,2,3";
    private static final String TEST_TENANT_KEY = "tld0nhuikk";

    @BeforeEach
    void setUp() {
        // 不在 setUp 中设置通用的 stub，而是在需要的测试用例中设置
    }

    @Test
    @DisplayName("正常执行逾期档案归档通知流程")
    void testNotificationOfOverdueFile_Normal() {
        // Mock cmicProperties
        when(cmicProperties.getHostTenantKey()).thenReturn(TEST_TENANT_KEY);

        // 准备测试数据
        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .build();

        Map<String, Object> recordData = new HashMap<>();
        recordData.put("REQUESTID", TEST_REQUEST_ID);
        recordData.put("KE_S", "dept001");
        recordData.put("CHAO_TSWTJTXJBRSJL", 3);
        recordData.put("CHAO_TSWTJTXJBMLDBM", 5);
        recordData.put("CHAO_TSWTJTXJBBMGSFGLD", 7);

        Map<String, Object> hrmDeptMap = new HashMap<>();
        hrmDeptMap.put("SSZJ", "user1,user2");
        hrmDeptMap.put("BMFGLD1", "user3");
        hrmDeptMap.put("GSFGLD", "user4");

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), any()))
                .thenReturn(Arrays.asList(recordData));
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Collections.singletonMap("USERID", TEST_USER_ID))
                .thenReturn(hrmDeptMap);

        // 执行测试
        notificationOfOverdueFileAction.notificationOfOverdueFile(Collections.singletonMap("userId", 1L));

        // 验证调用
        verify(dataBaseService).getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理");
        verify(superviseMessageManager).getOvertimeConfigByTableName(TEST_TABLE_NAME);
        verify(dataSqlService, times(2)).executeCommonSqlAll(anyString(), any(), any(), any());
    }

    @Test
    @DisplayName("超时配置为空的情况")
    void testNotificationOfOverdueFile_OvertimeConfigNull() {
        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(null);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), any()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        notificationOfOverdueFileAction.notificationOfOverdueFile(Collections.singletonMap("userId", 1L));

        // 验证调用
        verify(dataBaseService).getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理");
        verify(superviseMessageManager).getOvertimeConfigByTableName(TEST_TABLE_NAME);
    }

    @Test
    @DisplayName("数据结果为空的情况")
    void testNotificationOfOverdueFile_EmptyDataResult() {
        // 准备测试数据
        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .build();

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), any()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        notificationOfOverdueFileAction.notificationOfOverdueFile(Collections.singletonMap("userId", 1L));

        // 验证调用
        verify(dataBaseService).getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理");
        verify(superviseMessageManager).getOvertimeConfigByTableName(TEST_TABLE_NAME);
        verify(dataSqlService).executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), any());
    }

    @Test
    @DisplayName("测试发送短信超时提醒_正常情况")
    void testOverTimeSendSMS_Normal() {
        // Mock cmicProperties
        when(cmicProperties.getHostTenantKey()).thenReturn(TEST_TENANT_KEY);

        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 0);

        Map<String, Object> handOperatorMap = new HashMap<>();
        handOperatorMap.put("LASTNAME", "张三");

        Map<String, Object> receiverUserMap = new HashMap<>();
        receiverUserMap.put("LOGIN_VALUE", "receiver001");
        receiverUserMap.put("MOBILE", "13800138000");

        List<Map<String, Object>> smsCountResult = Arrays.asList(
                Collections.singletonMap("TIMES", 0)
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData))
                .thenReturn(smsCountResult);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);
        when(dataBaseService.getEmployeeInfo("handUser001")).thenReturn(handOperatorMap);
        when(dataBaseService.getEmployeeInfo("receiveUser001")).thenReturn(receiverUserMap);

        // 执行测试
        notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID,1L);

        // 验证调用
        verify(smsRemindManager).sendCommonSMS(eq(TEST_REQUEST_ID), eq("creator001"), eq("receiveUser001"),
                eq("13800138000"), eq("档案归档逾期提醒"), anyString(), eq("13"), eq(1L));
    }

    @Test
    @DisplayName("测试发送短信超时提醒_工单不需要提醒")
    void testOverTimeSendSMS_NoRemindNeeded() {
        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 1); // 设置为1，表示不需要提醒

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData));
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);

        // 执行测试
        notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID,1L);

        // 验证不应该发送短信
        verify(smsRemindManager, never()).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试发送短信超时提醒_已达到最大提醒次数")
    void testOverTimeSendSMS_MaxRemindTimesReached() {
        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 0);

        Map<String, Object> handOperatorMap = new HashMap<>();
        handOperatorMap.put("LASTNAME", "张三");

        Map<String, Object> receiverUserMap = new HashMap<>();
        receiverUserMap.put("LOGIN_VALUE", "receiver001");
        receiverUserMap.put("MOBILE", "13800138000");

        List<Map<String, Object>> smsCountResult = Arrays.asList(
                Collections.singletonMap("TIMES", 3) // 已达到最大提醒次数
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData))
                .thenReturn(smsCountResult);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);
        when(dataBaseService.getEmployeeInfo("handUser001")).thenReturn(handOperatorMap);
        when(dataBaseService.getEmployeeInfo("receiveUser001")).thenReturn(receiverUserMap);

        // 执行测试
        notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID,1L);

        // 验证不应该发送短信
        verify(smsRemindManager, never()).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试发送短信超时提醒_接收人为空")
    void testOverTimeSendSMS_EmptyReceiver() {
        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 0);

        Map<String, Object> handOperatorMap = new HashMap<>();
        handOperatorMap.put("LASTNAME", "张三");

        Map<String, Object> receiverUserMap = new HashMap<>();
        receiverUserMap.put("LOGIN_VALUE", ""); // 接收人为空
        receiverUserMap.put("MOBILE", "13800138000");

        List<Map<String, Object>> smsCountResult = Arrays.asList(
                Collections.singletonMap("TIMES", 0)
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData))
                .thenReturn(smsCountResult);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);
        when(dataBaseService.getEmployeeInfo("handUser001")).thenReturn(handOperatorMap);
        when(dataBaseService.getEmployeeInfo("receiveUser001")).thenReturn(receiverUserMap);

        // 执行测试
        notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID,1L);

        // 验证不应该发送短信
        verify(smsRemindManager, never()).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试获取当前环节处理人_通过反射")
    void testGetHandUserId_ViaReflection() throws Exception {
        // 准备测试数据
        List<Map<String, Object>> dataResult = Arrays.asList(
                Collections.singletonMap("USERID", TEST_USER_ID)
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(dataResult);

        // 使用反射调用私有方法
        Method getHandUserIdMethod = NotificationOfOverdueFileAction.class.getDeclaredMethod("getHandUserId", String.class);
        getHandUserIdMethod.setAccessible(true);

        String result = (String) getHandUserIdMethod.invoke(notificationOfOverdueFileAction, TEST_REQUEST_ID);

        assertEquals(TEST_USER_ID, result);
        verify(dataSqlService).executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList());
    }

    @Test
    @DisplayName("测试获取当前环节处理人_空结果通过反射")
    void testGetHandUserId_EmptyResult_ViaReflection() throws Exception {
        // Mock 方法调用返回空结果
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 使用反射调用私有方法
        Method getHandUserIdMethod = NotificationOfOverdueFileAction.class.getDeclaredMethod("getHandUserId", String.class);
        getHandUserIdMethod.setAccessible(true);

        String result = (String) getHandUserIdMethod.invoke(notificationOfOverdueFileAction, TEST_REQUEST_ID);

        assertNull(result);
        verify(dataSqlService).executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList());
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testNotificationOfOverdueFile_ExceptionHandling() {
        // Mock 异常情况
        when(dataBaseService.getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理"))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试，验证方法可以正常执行（可能会抛出异常，这是正常的）
        try {
            notificationOfOverdueFileAction.notificationOfOverdueFile(Collections.singletonMap("userId", 1L));
        } catch (RuntimeException e) {
            // 异常是预期的，验证异常消息
            assertEquals("数据库连接异常", e.getMessage());
        }

        // 验证方法被调用
        verify(dataBaseService).getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理");
    }

    @Test
    @DisplayName("测试部门经理提醒功能")
    void testNotificationOfOverdueFile_DepartmentManagerRemind() {
        // 准备测试数据
        OvertimeConfig overtimeConfig = OvertimeConfig.builder()
                .selectNodeId(TEST_SELECT_NODE_ID)
                .build();

        Map<String, Object> recordData = new HashMap<>();
        recordData.put("REQUESTID", TEST_REQUEST_ID);
        recordData.put("KE_S", "dept001");
        recordData.put("CHAO_TSWTJTXJBRSJL", 0); // 室经理提醒天数为0
        recordData.put("CHAO_TSWTJTXJBBMLD", 5); // 部门经理提醒天数为5
        recordData.put("CHAO_TSWTJTXJBBMGSFGLD", 0); // 公司分管领导提醒天数为0

        Map<String, Object> hrmDeptMap = new HashMap<>();
        hrmDeptMap.put("SSZJ", "");
        hrmDeptMap.put("BMFGLD1", "user3,user4");
        hrmDeptMap.put("GSFGLD", "");

        // Mock 方法调用
        when(dataBaseService.getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理"))
                .thenReturn(TEST_TABLE_NAME);
        when(superviseMessageManager.getOvertimeConfigByTableName(TEST_TABLE_NAME))
                .thenReturn(overtimeConfig);
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), any()))
                .thenReturn(Arrays.asList(recordData));
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Collections.singletonMap("USERID", TEST_USER_ID))
                .thenReturn(hrmDeptMap);

        // 执行测试
        notificationOfOverdueFileAction.notificationOfOverdueFile(Collections.singletonMap("userId", 1L));

        // 验证调用
        verify(dataBaseService).getBaseDataValue("逾期档案归档通知单流程(子流程)表名称", "督办管理");
        verify(superviseMessageManager).getOvertimeConfigByTableName(TEST_TABLE_NAME);
    }

    @Test
    @DisplayName("测试发送短信异常处理")
    void testOverTimeSendSMS_SMSException() {
        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 0);

        Map<String, Object> handOperatorMap = new HashMap<>();
        handOperatorMap.put("LASTNAME", "张三");

        Map<String, Object> receiverUserMap = new HashMap<>();
        receiverUserMap.put("LOGIN_VALUE", "receiver001");
        receiverUserMap.put("MOBILE", "13800138000");

        List<Map<String, Object>> smsCountResult = Arrays.asList(
                Collections.singletonMap("TIMES", 0)
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData))
                .thenReturn(smsCountResult);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);
        when(dataBaseService.getEmployeeInfo("handUser001")).thenReturn(handOperatorMap);
        when(dataBaseService.getEmployeeInfo("receiveUser001")).thenReturn(receiverUserMap);

        // Mock 短信发送异常
        doThrow(new RuntimeException("短信发送失败")).when(smsRemindManager)
                .sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());

        // 执行测试，预期可能会抛出异常
        try {
            notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID, 1L);
        } catch (RuntimeException e) {
            // 验证异常消息
            assertEquals("短信发送失败", e.getMessage());
        }

        // 验证短信服务被调用
        verify(smsRemindManager).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试空环节ID情况")
    void testOverTimeSendSMS_EmptySelectNodeId() {
        // 准备测试数据
        Map<String, Object> workflowData = new HashMap<>();
        workflowData.put("REQUESTNAME", "测试流程");
        workflowData.put("CURRENTNODEID", "node001");
        workflowData.put("CREATER", "creator001");

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("SUM", 0);

        Map<String, Object> handOperatorMap = new HashMap<>();
        handOperatorMap.put("LASTNAME", "张三");

        Map<String, Object> receiverUserMap = new HashMap<>();
        receiverUserMap.put("LOGIN_VALUE", "receiver001");
        receiverUserMap.put("MOBILE", "13800138000");

        List<Map<String, Object>> smsCountResult = Arrays.asList(
                Collections.singletonMap("TIMES", 0)
        );

        // Mock 方法调用
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Arrays.asList(workflowData))
                .thenReturn(smsCountResult);
        when(dataSqlService.executeCommonSqlOne(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.WORKFLOW_LIST.getGroupId()), anyList()))
                .thenReturn(countMap);
        when(dataBaseService.getEmployeeInfo("handUser001")).thenReturn(handOperatorMap);
        when(dataBaseService.getEmployeeInfo("receiveUser001")).thenReturn(receiverUserMap);

        // 执行测试 - 空环节ID
        notificationOfOverdueFileAction.overTimeSendSMS(3, "", 1, "receiveUser001", "handUser001", TEST_REQUEST_ID, 1L);

        // 验证SQL查询被调用（不包含环节ID条件）
        verify(dataSqlService, times(2)).executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList());
        verify(smsRemindManager).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("测试流程数据为空情况")
    void testOverTimeSendSMS_EmptyWorkflowData() {
        // Mock 方法调用返回空结果
        when(dataSqlService.executeCommonSqlAll(anyString(), eq(SourceType.LOGIC),
                eq(DsLogicGroupIdEnum.EBUILDER_FORM.getGroupId()), anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        notificationOfOverdueFileAction.overTimeSendSMS(3, TEST_SELECT_NODE_ID, 1, "receiveUser001", "handUser001", TEST_REQUEST_ID, 1L);

        // 验证短信服务不会被调用
        verify(smsRemindManager, never()).sendCommonSMS(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyLong());
    }
} 