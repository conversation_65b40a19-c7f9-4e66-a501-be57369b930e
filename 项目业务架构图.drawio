<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-25T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="泛微Ecology二次开发项目业务架构图" id="architecture">
    <mxGraphModel dx="1800" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="1400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 标题 -->
        <mxCell id="title" value="泛微Ecology二次开发项目业务架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="700" y="30" width="600" height="50" as="geometry" />
        </mxCell>
        
        <!-- 外部系统层 -->
        <mxCell id="external-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#4caf50;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="1900" height="140" as="geometry" />
        </mxCell>
        <mxCell id="external-title" value="外部系统层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="85" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 外部系统模块 -->
        <mxCell id="ecology" value="泛微Ecology系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="150" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="gateway" value="安全网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="450" y="150" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="portal" value="Portal认证中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="700" y="150" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mobile" value="移动端应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="950" y="150" width="180" height="50" as="geometry" />
        </mxCell>
        
        <!-- 用户界面层 -->
        <mxCell id="interface-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#4caf50;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="270" width="1900" height="140" as="geometry" />
        </mxCell>
        <mxCell id="interface-title" value="用户界面层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="255" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 用户界面层模块 -->
        <mxCell id="restapi" value="RESTful API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="150" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="controller" value="Controller层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sso" value="SSO单点登录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="550" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="auth" value="权限控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="750" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="exception" value="异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="950" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        
        <!-- 应用服务层 -->
        <mxCell id="service-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="440" width="1900" height="200" as="geometry" />
        </mxCell>
        <mxCell id="service-title" value="应用服务层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="425" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 核心业务服务组 -->
        <mxCell id="business-group" value="核心业务服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=rgba(255,255,255,0.8);strokeColor=#4caf50;fontSize=16;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="470" width="1200" height="80" as="geometry" />
        </mxCell>

        <!-- 核心业务服务 -->
        <mxCell id="invoice-service" value="发票管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="park-service" value="园区管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="320" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="legal-service" value="法务管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="510" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="appeal-service" value="申诉处理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="700" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="archive-service" value="档案管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="890" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="carpool-service" value="拼车服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1080" y="490" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 集成服务组 -->
        <mxCell id="integration-group" value="集成服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=rgba(255,255,255,0.8);strokeColor=#4caf50;fontSize=16;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1350" y="470" width="550" height="140" as="geometry" />
        </mxCell>

        <!-- 集成服务 -->
        <mxCell id="workflow" value="工作流集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1380" y="490" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rpc" value="RPC远程服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1550" y="490" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="job" value="定时任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1720" y="490" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="message" value="消息推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1465" y="550" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 基础服务层 -->
        <mxCell id="foundation-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#4caf50;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="670" width="1900" height="200" as="geometry" />
        </mxCell>
        <mxCell id="foundation-title" value="基础服务层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="655" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 数据服务组 -->
        <mxCell id="data-service-group" value="数据服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=rgba(255,255,255,0.8);strokeColor=#4caf50;fontSize=16;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="700" width="600" height="80" as="geometry" />
        </mxCell>

        <!-- 数据服务 -->
        <mxCell id="data-access" value="数据访问服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="720" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cache" value="缓存服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="310" y="720" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="export" value="导出服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="490" y="720" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 工具组件组 -->
        <mxCell id="util-group" value="工具组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=rgba(255,255,255,0.8);strokeColor=#4caf50;fontSize=16;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="750" y="700" width="1150" height="140" as="geometry" />
        </mxCell>

        <!-- 工具组件 -->
        <mxCell id="http-util" value="HTTP工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="780" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="encrypt-util" value="加密工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="940" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="page-util" value="分页工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1100" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="convert-util" value="转换工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1260" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="date-util" value="日期工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1420" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sso-util" value="SSO工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1580" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="workflow-util" value="工作流工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1740" y="720" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="string-util" value="字符串工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="860" y="780" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="list-util" value="集合工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1020" y="780" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sql-util" value="SQL工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1180" y="780" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ip-util" value="IP工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1340" y="780" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sms-util" value="短信工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1500" y="780" width="130" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据持久层 -->
        <mxCell id="data-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4caf50;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="900" width="1900" height="140" as="geometry" />
        </mxCell>
        <mxCell id="data-title" value="数据持久层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="885" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 数据持久层模块 -->
        <mxCell id="ebuilder" value="EBuilder表单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="950" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="database" value="数据库访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="450" y="950" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="700" y="950" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="config" value="配置管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="950" y="950" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="tenant" value="多租户支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1200" y="950" width="180" height="50" as="geometry" />
        </mxCell>
        
        <!-- 连接线 - 主要数据流 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=3;" edge="1" parent="1" source="ecology" target="restapi">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="200" as="sourcePoint" />
            <mxPoint x="230" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=3;" edge="1" parent="1" source="restapi" target="invoice-service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="370" as="sourcePoint" />
            <mxPoint x="210" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=3;" edge="1" parent="1" source="invoice-service" target="data-access">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="530" as="sourcePoint" />
            <mxPoint x="205" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=3;" edge="1" parent="1" source="data-access" target="ebuilder">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="205" y="760" as="sourcePoint" />
            <mxPoint x="290" y="950" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 横向连接线 - 显示层级间关系 -->
        <mxCell id="layer-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="240" as="sourcePoint" />
            <mxPoint x="1000" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="layer-arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="410" as="sourcePoint" />
            <mxPoint x="1000" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="layer-arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="640" as="sourcePoint" />
            <mxPoint x="1000" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="layer-arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="870" as="sourcePoint" />
            <mxPoint x="1000" y="900" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例 -->
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1080" width="1900" height="120" as="geometry" />
        </mxCell>
        <mxCell id="legend-title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="80" y="1100" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 图例项目 -->
        <mxCell id="legend-external" value="外部系统层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#4caf50;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="200" y="1110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-interface" value="用户界面层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#4caf50;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="320" y="1110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-service" value="应用服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="440" y="1110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-foundation" value="基础服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#4caf50;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="560" y="1110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-data" value="数据持久层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4caf50;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="680" y="1110" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 箭头图例 -->
        <mxCell id="legend-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="820" y="1125" as="sourcePoint" />
            <mxPoint x="870" y="1125" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-arrow1-text" value="主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="880" y="1110" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="980" y="1125" as="sourcePoint" />
            <mxPoint x="1030" y="1125" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-arrow2-text" value="层级关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1040" y="1110" width="80" height="30" as="geometry" />
        </mxCell>

        <!-- 技术栈说明 -->
        <mxCell id="tech-stack" value="技术栈：Java Spring Boot + 泛微Ecology + Dubbo + Redis + MySQL/Oracle" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="200" y="1150" width="800" height="30" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
