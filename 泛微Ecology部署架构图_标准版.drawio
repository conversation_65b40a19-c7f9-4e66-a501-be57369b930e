<mxfile host="app.diagrams.net" modified="2025-01-27T12:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="泛微Ecology生产环境部署架构" id="ecology-deployment">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 环境标识 -->
        <mxCell id="env-label" value="生产环境部署架构" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1300" y="20" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- 客户端层 -->
        <mxCell id="client-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="client-layer-label" value="客户端层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="70" y="90" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- PC端客户端 -->
        <mxCell id="pc-browser" value="🖥️ PC浏览器" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="pc-client" value="🌐 CS客户端" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="350" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- 移动端客户端 -->
        <mxCell id="mobile-h5" value="📱 H5应用" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="800" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-wechat" value="💬 微信小程序" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="950" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-app" value="📲 移动APP" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1100" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- 第三方系统 -->
        <mxCell id="third-party" value="🔗 第三方系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=1;fontColor=#1976D2;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1300" y="130" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- HTTP/HTTPS连接线 -->
        <mxCell id="http-flow" value="HTTP/HTTPS" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=3;fontSize=12;fontColor=#2196F3;labelBackgroundColor=#ffffff;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="220" as="sourcePoint" />
            <mxPoint x="800" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 网关层 -->
        <mxCell id="gateway-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="280" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="gateway-layer-label" value="网关层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="70" y="290" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- NGINX负载均衡 -->
        <mxCell id="nginx-lb" value="🟢 NGINX&#xa;负载均衡器" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Spring Cloud Gateway -->
        <mxCell id="spring-gateway" value="🍃 Spring Cloud&#xa;Gateway" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="500" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- API网关 -->
        <mxCell id="api-gateway" value="🔐 API网关" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="700" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 安全网关 -->
        <mxCell id="security-gateway" value="🛡️ 安全网关" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="900" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Request/Response流向 -->
        <mxCell id="request-flow" value="Request" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;fontSize=11;fontColor=#2196F3;labelBackgroundColor=#ffffff;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="420" as="sourcePoint" />
            <mxPoint x="600" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="response-flow" value="Response" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;fontSize=11;fontColor=#2196F3;labelBackgroundColor=#ffffff;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="900" y="460" as="sourcePoint" />
            <mxPoint x="900" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 服务层 -->
        <mxCell id="service-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="480" width="1500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="service-layer-label" value="服务层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="70" y="490" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- 泛微Ecology核心 -->
        <mxCell id="ecology-core" value="🏢 泛微Ecology&#xa;核心平台" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="530" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Spring Boot集群 -->
        <mxCell id="springboot-cluster-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="380" y="520" width="280" height="80" as="geometry" />
        </mxCell>
        <mxCell id="springboot-cluster-label" value="Spring Boot集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="490" y="525" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="springboot-1" value="🍃 实例1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="400" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="springboot-2" value="🍃 实例2" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="480" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="springboot-3" value="🍃 实例3" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="560" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <!-- Dubbo RPC服务集群 -->
        <mxCell id="dubbo-cluster-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="700" y="520" width="280" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dubbo-cluster-label" value="Dubbo RPC集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="810" y="525" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="dubbo-1" value="⚡ 人员同步" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="720" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dubbo-2" value="⚡ 组织同步" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="800" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dubbo-3" value="⚡ 工作流" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="880" y="550" width="70" height="40" as="geometry" />
        </mxCell>
        
        <!-- Portal认证中心 -->
        <mxCell id="portal-auth" value="🔑 Portal&#xa;认证中心" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1020" y="530" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- NACOS注册中心 -->
        <mxCell id="nacos-center" value="🎯 NACOS&#xa;注册中心" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1200" y="530" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 服务注册发现连接线 -->
        <mxCell id="service-register" value="服务注册/发现" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;fontSize=10;fontColor=#FF9800;labelBackgroundColor=#ffffff;strokeStyle=dashed;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="610" as="sourcePoint" />
            <mxPoint x="1200" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- RPC调用连接线 -->
        <mxCell id="rpc-call" value="Dubbo RPC" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;fontSize=10;fontColor=#4CAF50;labelBackgroundColor=#ffffff;strokeStyle=dashed;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="570" as="sourcePoint" />
            <mxPoint x="700" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据访问连接线 -->
        <mxCell id="data-access" value="数据访问" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;fontSize=11;fontColor=#FF9800;labelBackgroundColor=#ffffff;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="700" as="sourcePoint" />
            <mxPoint x="750" y="740" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据层 -->
        <mxCell id="data-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="760" width="1500" height="160" as="geometry" />
        </mxCell>
        <mxCell id="data-layer-label" value="数据层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="70" y="770" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 数据库集群 -->
        <mxCell id="db-cluster-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="200" y="800" width="300" height="100" as="geometry" />
        </mxCell>
        <mxCell id="db-cluster-label" value="数据库集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="320" y="805" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="db-master" value="🗄️ MySQL/Oracle&#xa;主库" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="230" y="830" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="db-slave1" value="🗄️ MySQL/Oracle&#xa;从库1" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="350" y="830" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- Redis缓存集群 -->
        <mxCell id="redis-cluster-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="550" y="800" width="300" height="100" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster-label" value="Redis缓存集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="670" y="805" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="redis-master" value="🔴 Redis&#xa;主节点" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="580" y="830" width="80" height="60" as="geometry" />
        </mxCell>

        <mxCell id="redis-slave1" value="🔴 Redis&#xa;从节点1" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="680" y="830" width="80" height="60" as="geometry" />
        </mxCell>

        <mxCell id="redis-slave2" value="🔴 Redis&#xa;从节点2" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="780" y="830" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- RocketMQ消息队列 -->
        <mxCell id="mq-cluster-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="900" y="800" width="300" height="100" as="geometry" />
        </mxCell>
        <mxCell id="mq-cluster-label" value="RocketMQ集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="1020" y="805" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="mq-nameserver" value="🚀 NameServer" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="930" y="830" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="mq-broker1" value="🚀 Broker1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1030" y="830" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="mq-broker2" value="🚀 Broker2" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1130" y="830" width="80" height="30" as="geometry" />
        </mxCell>

        <!-- 基础设施层 -->
        <mxCell id="infra-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="960" width="1500" height="160" as="geometry" />
        </mxCell>
        <mxCell id="infra-layer-label" value="基础设施层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#9E9E9E;" vertex="1" parent="1">
          <mxGeometry x="70" y="970" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 文件存储 -->
        <mxCell id="file-storage" value="☁️ 文件存储&#xa;(NFS/OSS)" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="200" y="1000" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 监控系统 -->
        <mxCell id="monitoring-system" value="📊 监控系统&#xa;(Prometheus)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 日志收集 -->
        <mxCell id="log-collection" value="📋 日志收集&#xa;(ELK Stack)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="560" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 链路追踪 -->
        <mxCell id="tracing-system" value="🔍 链路追踪&#xa;(Skywalking)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="740" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 配置中心 -->
        <mxCell id="config-center" value="⚙️ 配置中心&#xa;(NACOS)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="920" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 定时任务 -->
        <mxCell id="scheduler" value="⏰ 定时任务&#xa;(XXL-Job)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1100" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 熔断限流 -->
        <mxCell id="circuit-breaker" value="🛡️ 熔断限流&#xa;(Sentinel)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;spacingLeft=10;spacingRight=10;" vertex="1" parent="1">
          <mxGeometry x="1280" y="1000" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 监控连接线 -->
        <mxCell id="monitoring-connection" value="监控采集" style="endArrow=classic;html=1;rounded=0;strokeColor=#757575;strokeWidth=1;fontSize=10;fontColor=#757575;labelBackgroundColor=#ffffff;strokeStyle=dotted;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="1000" as="sourcePoint" />
            <mxPoint x="520" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 技术栈说明 -->
        <mxCell id="tech-stack" value="技术栈：Java 8 + Spring Boot 2.1.9 + Dubbo 2.7.15 + Redis + MySQL/Oracle + 泛微Ecology + Gradle构建" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="300" y="1150" width="1000" height="30" as="geometry" />
        </mxCell>

        <!-- 网络边界标识 -->
        <mxCell id="dmz-zone" value="DMZ区域" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=none;strokeColor=#FF9800;strokeWidth=2;strokeStyle=dashed;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="250" y="270" width="850" height="140" as="geometry" />
        </mxCell>

        <mxCell id="internal-zone" value="内网区域" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=none;strokeColor=#4CAF50;strokeWidth=2;strokeStyle=dashed;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="180" y="470" width="1200" height="470" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
