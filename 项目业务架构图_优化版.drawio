<mxfile host="app.diagrams.net" modified="2025-01-25T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="泛微Ecology二次开发项目业务架构图" id="architecture">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="泛微Ecology二次开发项目业务架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=32;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="800" y="40" width="600" height="60" as="geometry" />
        </mxCell>
        
        <!-- 外部系统层 -->
        <mxCell id="external-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#4caf50;strokeWidth=3;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="140" width="2000" height="160" as="geometry" />
        </mxCell>
        <mxCell id="external-title" value="外部系统层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="130" y="120" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ecology" value="泛微Ecology系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="300" y="190" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gateway" value="安全网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="190" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="portal" value="Portal认证中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="900" y="190" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mobile" value="移动端应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="190" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- 用户界面层 -->
        <mxCell id="interface-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#4caf50;strokeWidth=3;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="340" width="2000" height="160" as="geometry" />
        </mxCell>
        <mxCell id="interface-title" value="用户界面层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="130" y="320" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="restapi" value="RESTful API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="200" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="controller" value="Controller层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="420" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sso" value="SSO单点登录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="640" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="auth" value="权限控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="860" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="exception" value="异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1080" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 应用服务层 -->
        <mxCell id="service-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;strokeWidth=3;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="540" width="2000" height="240" as="geometry" />
        </mxCell>
        <mxCell id="service-title" value="应用服务层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="130" y="520" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 核心业务服务 -->
        <mxCell id="invoice-service" value="发票管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="park-service" value="园区管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="legal-service" value="法务管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="570" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="appeal-service" value="申诉处理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="780" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="archive-service" value="档案管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="990" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="carpool-service" value="拼车服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 集成服务 -->
        <mxCell id="workflow" value="工作流集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1450" y="590" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rpc" value="RPC远程服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1650" y="590" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="job" value="定时任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1450" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="message" value="消息推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1650" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 基础服务层 -->
        <mxCell id="foundation-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#4caf50;strokeWidth=3;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="820" width="2000" height="240" as="geometry" />
        </mxCell>
        <mxCell id="foundation-title" value="基础服务层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="130" y="800" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据服务 -->
        <mxCell id="data-access" value="数据访问服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="870" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cache" value="缓存服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="870" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="export" value="导出服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="570" y="870" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 工具组件 -->
        <mxCell id="http-util" value="HTTP工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="800" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="encrypt-util" value="加密工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="970" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="page-util" value="分页工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1140" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="convert-util" value="转换工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1310" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="date-util" value="日期工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1480" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sso-util" value="SSO工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1650" y="870" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="string-util" value="字符串工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="800" y="950" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="list-util" value="集合工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="970" y="950" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sql-util" value="SQL工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1140" y="950" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="workflow-util" value="工作流工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1310" y="950" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sms-util" value="短信工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=14;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1480" y="950" width="140" height="50" as="geometry" />
        </mxCell>
        
        <!-- 数据持久层 -->
        <mxCell id="data-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4caf50;strokeWidth=3;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="1100" width="2000" height="160" as="geometry" />
        </mxCell>
        <mxCell id="data-title" value="数据持久层" style="text;html=1;strokeColor=#4caf50;fillColor=#ffffff;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;fontColor=#2c3e50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="130" y="1080" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ebuilder" value="EBuilder表单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="300" y="1150" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="database" value="数据库访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="1150" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="900" y="1150" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config" value="配置管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="1150" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tenant" value="多租户支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#4caf50;fontSize=16;fontStyle=0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1500" y="1150" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- 主要数据流箭头 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=4;" edge="1" parent="1" source="ecology" target="restapi">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="250" as="sourcePoint" />
            <mxPoint x="290" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=4;" edge="1" parent="1" source="restapi" target="invoice-service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="450" as="sourcePoint" />
            <mxPoint x="240" y="590" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=4;" edge="1" parent="1" source="invoice-service" target="data-access">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="650" as="sourcePoint" />
            <mxPoint x="240" y="870" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4caf50;strokeWidth=4;" edge="1" parent="1" source="data-access" target="ebuilder">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="930" as="sourcePoint" />
            <mxPoint x="400" y="1150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 层级关系箭头 -->
        <mxCell id="layer-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=3;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="300" as="sourcePoint" />
            <mxPoint x="1100" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="layer-arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=3;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="500" as="sourcePoint" />
            <mxPoint x="1100" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="layer-arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=3;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="780" as="sourcePoint" />
            <mxPoint x="1100" y="820" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="layer-arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=3;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1100" y="1060" as="sourcePoint" />
            <mxPoint x="1100" y="1100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 技术栈说明 -->
        <mxCell id="tech-stack" value="技术栈：Java Spring Boot + 泛微Ecology + Dubbo RPC + Redis缓存 + MySQL/Oracle数据库 + EBuilder表单引擎" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="400" y="1300" width="1200" height="40" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
