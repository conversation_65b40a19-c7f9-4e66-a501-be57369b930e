# 泛微Ecology二次开发项目业务架构说明

## 项目概述

本项目是基于泛微Ecology系统的企业级二次开发项目，采用Java Spring Boot技术栈，主要服务于企业内部的发票管理、园区管理、法务管理、申诉处理、档案管理和拼车服务等核心业务场景。

## 技术栈

- **开发语言**: Java 8+
- **框架**: Spring Boot 2.1.9
- **构建工具**: Gradle
- **微服务**: Dubbo 2.7.15
- **数据库**: 支持MySQL、Oracle、PostgreSQL等
- **缓存**: Redis
- **工作流**: 泛微Ecology工作流引擎
- **认证**: SSO单点登录、安全网关

## 架构设计原则

### 1. 分层架构
严格遵循MVC分层结构：`action → controller → service → domain`

### 2. 模块化设计
- 按业务领域划分模块
- 每个模块包含完整的分层结构
- 模块间低耦合、高内聚

### 3. 统一规范
- 统一的包结构规范
- 统一的异常处理机制
- 统一的工具类和服务

## 业务架构层次

### 外部系统层
负责与外部系统的集成和交互

#### 泛微Ecology系统
- 核心OA平台
- 提供基础的用户管理、权限控制
- 工作流引擎支持

#### 安全网关
- 统一的安全认证入口
- API访问控制
- 安全策略执行

#### Portal认证中心
- 企业门户认证
- 用户身份验证
- Token管理

#### 移动端应用
- 移动办公支持
- 响应式接口设计
- 移动端专用功能

### 用户界面层
提供统一的API接口和用户交互

#### RESTful API
- 标准的REST接口设计
- JSON数据格式
- HTTP状态码规范

#### Controller层
- 请求路由处理
- 参数验证
- 响应格式化

#### SSO单点登录
- 统一身份认证
- 跨系统免登录
- 会话管理

#### 权限控制
- 基于角色的访问控制(RBAC)
- 细粒度权限管理
- 数据权限控制

#### 异常处理
- 全局异常捕获
- 统一错误响应格式
- 错误日志记录

### 应用服务层
核心业务逻辑处理层

#### 核心业务服务

**发票管理服务**
- 发票信息查询与管理
- 发票勾稽处理
- 红冲作废处理
- 应收单管理
- 财务数据校验

**园区管理服务**
- 工位信息管理
- 楼宇楼层管理
- 领导办公室管理
- 工位分配与释放
- 空间冲突检查

**法务管理服务**
- 法律案件管理
- 文档附件处理
- 案件流程跟踪
- 法务数据统计

**申诉处理服务**
- 诉求信息管理
- 申诉流程处理
- 反馈意见收集
- 处理结果跟踪

**档案管理服务**
- 档案信息存储
- 档案检索查询
- 档案权限控制
- 档案生命周期管理

**拼车服务**
- 拼车信息发布
- 拼车匹配服务
- 行程管理
- 费用结算

#### 集成服务

**工作流集成**
- 泛微工作流引擎集成
- 流程定义管理
- 流程实例控制
- 审批节点处理

**RPC远程服务**
- 基于Dubbo的服务调用
- 服务注册与发现
- 负载均衡
- 容错处理

**定时任务**
- 数据同步任务
- 定期清理任务
- 报表生成任务
- 系统监控任务

**消息推送**
- 实时消息通知
- 邮件推送
- 短信提醒
- 系统公告

### 基础服务层
提供通用的技术服务和工具支持

#### 数据服务

**数据访问服务**
- 统一的数据访问接口
- 多数据源支持
- 事务管理
- 连接池管理

**缓存服务**
- Redis缓存集成
- 缓存策略管理
- 缓存失效处理
- 分布式缓存

**导出服务**
- Excel导出功能
- PDF生成
- 多层标题模板
- 异步导出支持

#### 工具组件

**HTTP工具**
- HTTP客户端封装
- 请求重试机制
- 超时控制
- 响应处理

**加密工具**
- 数据加密解密
- 密码哈希
- 数字签名
- 安全传输

**分页工具**
- 统一分页接口
- 分页参数处理
- 分页结果封装
- 性能优化

**转换工具**
- 数据类型转换
- 对象映射
- 格式化处理
- 编码转换

**日期工具**
- 日期格式化
- 时区处理
- 日期计算
- 工作日判断

### 数据持久层
数据存储和访问的底层支持

#### EBuilder表单
- 泛微表单引擎
- 动态表单生成
- 表单数据管理
- 表单权限控制

#### 数据库访问
- 多数据库支持
- SQL优化
- 数据库连接管理
- 读写分离

#### 文件存储
- 文件上传下载
- 文件版本管理
- 存储空间管理
- 文件安全控制

#### 配置管理
- 系统配置管理
- 环境配置隔离
- 配置热更新
- 配置版本控制

## 数据流向

1. **请求流向**: 外部系统 → 用户界面层 → 应用服务层 → 基础服务层 → 数据持久层
2. **响应流向**: 数据持久层 → 基础服务层 → 应用服务层 → 用户界面层 → 外部系统
3. **异步处理**: 定时任务、消息队列等异步处理机制
4. **缓存策略**: 多级缓存提升系统性能

## 安全架构

### 认证机制
- SSO单点登录
- Token验证
- 安全网关认证
- 移动端认证

### 权限控制
- 基于角色的访问控制
- 数据权限控制
- API权限验证
- 操作审计日志

### 数据安全
- 敏感数据加密
- 传输加密
- 数据脱敏
- 备份恢复

## 性能优化

### 缓存策略
- Redis分布式缓存
- 本地缓存
- 查询结果缓存
- 静态资源缓存

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 读写分离

### 异步处理
- 异步任务处理
- 消息队列
- 批量处理
- 定时任务

## 监控与运维

### 系统监控
- 应用性能监控
- 数据库监控
- 缓存监控
- 接口调用监控

### 日志管理
- 统一日志格式
- 日志分级管理
- 日志收集分析
- 错误告警

### 部署架构
- 微服务部署
- 容器化支持
- 负载均衡
- 高可用设计

## 扩展性设计

### 模块扩展
- 插件化架构
- 模块热插拔
- 接口标准化
- 配置驱动

### 性能扩展
- 水平扩展支持
- 服务拆分
- 数据分片
- 缓存扩展

### 功能扩展
- 工作流扩展
- 报表扩展
- 集成扩展
- 移动端扩展
