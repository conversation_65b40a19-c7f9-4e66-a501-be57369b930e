<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>泛微Ecology二次开发项目业务架构图</title>
    <style>
        /* 主题变量定义 */
        :root {
            --border-color: #4caf50;
            --external-bg: #f5f5f5;
            --interface-bg: #e3f2fd;
            --service-bg: #e8f5e8;
            --foundation-bg: #fff8e1;
            --data-bg: #f3e5f5;
            --module-bg: rgba(255, 255, 255, 0.8);
            --border-radius: 3px;
            --font-size: 13px;
            --module-padding: 6px;
            --layer-gap: 15px;
            --module-gap: 10px;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .architecture-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 30px;
            border-bottom: 3px solid var(--border-color);
            padding-bottom: 15px;
        }

        .architecture-layers {
            display: flex;
            flex-direction: column;
            gap: var(--layer-gap);
        }

        .layer {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            position: relative;
        }

        .layer-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            background: white;
            padding: 5px 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            display: inline-block;
            position: absolute;
            top: -15px;
            left: 20px;
        }

        .modules-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--module-gap);
            margin-top: 10px;
        }

        .module-group {
            background: var(--module-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            flex: 1;
            min-width: 200px;
        }

        .module-group-title {
            font-size: 14px;
            font-weight: bold;
            color: #34495e;
            margin-bottom: 10px;
            text-align: center;
        }

        .modules {
            display: flex;
            flex-wrap: wrap;
            gap: var(--module-gap);
        }

        .module {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--module-padding);
            font-size: var(--font-size);
            white-space: nowrap;
            text-align: center;
            min-width: 100px;
            flex: 1;
            transition: all 0.3s ease;
        }

        .module:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        /* 层级背景色 */
        .external-layer { background: var(--external-bg); }
        .interface-layer { background: var(--interface-bg); }
        .service-layer { background: var(--service-bg); }
        .foundation-layer { background: var(--foundation-bg); }
        .data-layer { background: var(--data-bg); }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modules-container {
                flex-direction: column;
            }
            
            .module-group {
                min-width: 100%;
            }
            
            .modules {
                justify-content: center;
            }
        }

        .legend {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: var(--border-radius);
            border: 1px solid #dee2e6;
        }

        .legend-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <h1 class="title">泛微Ecology二次开发项目业务架构图</h1>
        
        <div class="architecture-layers">
            <!-- 外部系统层 -->
            <div class="layer external-layer">
                <div class="layer-title">外部系统层</div>
                <div class="modules-container">
                    <div class="module-group">
                        <div class="modules">
                            <div class="module">泛微Ecology系统</div>
                            <div class="module">安全网关</div>
                            <div class="module">Portal认证中心</div>
                            <div class="module">移动端应用</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户界面层 -->
            <div class="layer interface-layer">
                <div class="layer-title">用户界面层</div>
                <div class="modules-container">
                    <div class="module-group">
                        <div class="module-group-title">接口服务</div>
                        <div class="modules">
                            <div class="module">RESTful API</div>
                            <div class="module">Controller层</div>
                        </div>
                    </div>
                    <div class="module-group">
                        <div class="module-group-title">安全认证</div>
                        <div class="modules">
                            <div class="module">SSO单点登录</div>
                            <div class="module">权限控制</div>
                            <div class="module">异常处理</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 应用服务层 -->
            <div class="layer service-layer">
                <div class="layer-title">应用服务层</div>
                <div class="modules-container">
                    <div class="module-group">
                        <div class="module-group-title">核心业务服务</div>
                        <div class="modules">
                            <div class="module">发票管理服务</div>
                            <div class="module">园区管理服务</div>
                            <div class="module">法务管理服务</div>
                            <div class="module">申诉处理服务</div>
                            <div class="module">档案管理服务</div>
                            <div class="module">拼车服务</div>
                        </div>
                    </div>
                    <div class="module-group">
                        <div class="module-group-title">集成服务</div>
                        <div class="modules">
                            <div class="module">工作流集成</div>
                            <div class="module">RPC远程服务</div>
                            <div class="module">定时任务</div>
                            <div class="module">消息推送</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础服务层 -->
            <div class="layer foundation-layer">
                <div class="layer-title">基础服务层</div>
                <div class="modules-container">
                    <div class="module-group">
                        <div class="module-group-title">数据服务</div>
                        <div class="modules">
                            <div class="module">数据访问服务</div>
                            <div class="module">缓存服务</div>
                            <div class="module">导出服务</div>
                        </div>
                    </div>
                    <div class="module-group">
                        <div class="module-group-title">工具组件</div>
                        <div class="modules">
                            <div class="module">HTTP工具</div>
                            <div class="module">加密工具</div>
                            <div class="module">分页工具</div>
                            <div class="module">转换工具</div>
                            <div class="module">日期工具</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据持久层 -->
            <div class="layer data-layer">
                <div class="layer-title">数据持久层</div>
                <div class="modules-container">
                    <div class="module-group">
                        <div class="modules">
                            <div class="module">EBuilder表单</div>
                            <div class="module">数据库访问</div>
                            <div class="module">文件存储</div>
                            <div class="module">配置管理</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图例说明 -->
        <div class="legend">
            <div class="legend-title">架构层级说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--external-bg);"></div>
                    <span>外部系统层 - 外部依赖系统</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--interface-bg);"></div>
                    <span>用户界面层 - API接口和认证</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--service-bg);"></div>
                    <span>应用服务层 - 核心业务逻辑</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--foundation-bg);"></div>
                    <span>基础服务层 - 通用服务组件</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--data-bg);"></div>
                    <span>数据持久层 - 数据存储访问</span>
                </div>
            </div>
        </div>

        <!-- 业务模块详细说明 -->
        <div class="legend" style="margin-top: 20px;">
            <div class="legend-title">核心业务模块说明</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">发票管理模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 发票信息查询与管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 发票勾稽处理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 红冲作废处理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 应收单管理</p>
                </div>
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">园区管理模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 工位信息管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 楼宇楼层管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 领导办公室管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 工位分配与释放</p>
                </div>
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">法务管理模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 法律案件管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 文档附件处理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 案件流程跟踪</p>
                </div>
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">申诉处理模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 诉求信息管理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 申诉流程处理</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 反馈意见收集</p>
                </div>
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">档案管理模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 档案信息存储</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 档案检索查询</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 档案权限控制</p>
                </div>
                <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;">
                    <h4 style="color: var(--border-color); margin-top: 0;">拼车服务模块</h4>
                    <p style="margin: 5px 0; font-size: 12px;">• 拼车信息发布</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 拼车匹配服务</p>
                    <p style="margin: 5px 0; font-size: 12px;">• 行程管理</p>
                </div>
            </div>
        </div>

        <!-- 技术特性说明 -->
        <div class="legend" style="margin-top: 20px;">
            <div class="legend-title">技术架构特性</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid var(--border-color);">
                    <h4 style="margin-top: 0; color: #2c3e50;">分层架构设计</h4>
                    <p style="margin: 5px 0; font-size: 12px;">严格遵循MVC分层结构，action → controller → service → domain</p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid var(--border-color);">
                    <h4 style="margin-top: 0; color: #2c3e50;">微服务集成</h4>
                    <p style="margin: 5px 0; font-size: 12px;">基于Dubbo的RPC服务调用，支持服务治理和负载均衡</p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid var(--border-color);">
                    <h4 style="margin-top: 0; color: #2c3e50;">工作流引擎</h4>
                    <p style="margin: 5px 0; font-size: 12px;">深度集成泛微工作流引擎，支持复杂业务流程</p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid var(--border-color);">
                    <h4 style="margin-top: 0; color: #2c3e50;">安全认证</h4>
                    <p style="margin: 5px 0; font-size: 12px;">SSO单点登录、权限控制、安全网关集成</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
