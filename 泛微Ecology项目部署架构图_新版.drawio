<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="泛微Ecology部署架构图" id="deployment-architecture">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="1000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 环境标识 -->
        <mxCell id="env-label" value="生产环境部署架构" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1100" y="20" width="200" height="30" as="geometry" />
        </mxCell>

        <!-- 客户端层大容器 -->
        <mxCell id="client-container" value="客户端" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;verticalAlign=top;spacingTop=15;" vertex="1" parent="1">
          <mxGeometry x="40" y="60" width="1320" height="90" as="geometry" />
        </mxCell>

        <!-- PC端组 -->
        <mxCell id="pc-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=#1976D2;strokeStyle=dashed;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="90" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="pc-browser" value="🖥️ BS端" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="85" y="105" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="cs-browser" value="🌐 CS端" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="175" y="105" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 第三方组 -->
        <mxCell id="third-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=#1976D2;strokeStyle=dashed;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="90" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="third-party" value="🔗 第三方" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="325" y="105" width="70" height="20" as="geometry" />
        </mxCell>

        <!-- 移动端组 -->
        <mxCell id="mobile-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=#1976D2;strokeStyle=dashed;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="90" width="450" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mobile-h5" value="📱 H5" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FF5722;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="870" y="105" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mobile-wechat" value="💬 微信小程序" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="940" y="105" width="90" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mobile-apple" value="🍎 苹果应用" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1040" y="105" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mobile-android" value="🤖 安卓应用" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1130" y="105" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- HTTP/HTTPS连接线 -->
        <mxCell id="http-arrow" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="160" as="sourcePoint" />
            <mxPoint x="700" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="http-label" value="HTTP/HTTPS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2196F3;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="620" y="165" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- NGINX负载均衡层 -->
        <mxCell id="nginx-container" value="前端服务器/负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;verticalAlign=top;spacingTop=15;" vertex="1" parent="1">
          <mxGeometry x="40" y="200" width="1320" height="70" as="geometry" />
        </mxCell>
        <mxCell id="nginx-logo" value="🟢 NGINX" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fillColor=#4CAF50;strokeColor=none;fontColor=white;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="230" width="120" height="35" as="geometry" />
        </mxCell>

        <!-- Request/Response连接线 -->
        <mxCell id="request-arrow" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="280" as="sourcePoint" />
            <mxPoint x="600" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="request-label" value="Request" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="520" y="285" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="response-arrow" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="310" as="sourcePoint" />
            <mxPoint x="800" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="response-label" value="Response" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="820" y="285" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 服务网关层 -->
        <mxCell id="gateway-container" value="服务网关(集群)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;verticalAlign=top;spacingTop=15;" vertex="1" parent="1">
          <mxGeometry x="40" y="320" width="1320" height="70" as="geometry" />
        </mxCell>
        <mxCell id="spring-gateway" value="🍃 Spring Cloud Gateway" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fillColor=#4CAF50;strokeColor=none;fontColor=white;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="350" width="220" height="35" as="geometry" />
        </mxCell>

        <!-- 注册/发现和Get配置连接线 -->
        <mxCell id="register-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="400" as="sourcePoint" />
            <mxPoint x="200" y="430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="register-label1" value="注册/发现" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="120" y="405" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="config-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="430" as="sourcePoint" />
            <mxPoint x="300" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="config-label1" value="Get配置" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="320" y="405" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- NACOS服务注册中心 -->
        <mxCell id="nacos-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffffff;strokeColor=#FF9800;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="440" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="nacos-logo" value="NACOS" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fillColor=#FF9800;strokeColor=none;fontColor=white;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="460" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nacos-desc" value="服务注册中心" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="495" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- Netty连接线 -->
        <mxCell id="netty-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="600" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="netty-label1" value="Netty" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="520" y="410" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="netty-arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="440" as="sourcePoint" />
            <mxPoint x="800" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="netty-label2" value="Netty" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="820" y="410" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 应用服务集群1 -->
        <mxCell id="app-service-container1" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="260" y="440" width="220" height="90" as="geometry" />
        </mxCell>
        <mxCell id="app-service-title1" value="应用服务" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=13;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="450" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="microservice1-1" value="🍃 微服务1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="280" y="475" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="microservice1-2" value="🍃 微服务2" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="380" y="475" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="microservice1-3" value="🍃 微服务..." style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="330" y="500" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 应用服务集群2 -->
        <mxCell id="app-service-container2" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="520" y="440" width="220" height="90" as="geometry" />
        </mxCell>
        <mxCell id="app-service-title2" value="应用服务" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=13;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="450" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="microservice2-1" value="🍃 微服务1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="540" y="475" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="microservice2-2" value="🍃 微服务2" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="640" y="475" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="microservice2-3" value="🍃 微服务..." style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="590" y="500" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 数据存储层连接线 -->
        <mxCell id="storage-arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="540" as="sourcePoint" />
            <mxPoint x="370" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="storage-arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="630" y="540" as="sourcePoint" />
            <mxPoint x="630" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="storage-arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="540" as="sourcePoint" />
            <mxPoint x="890" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Redis集群 -->
        <mxCell id="redis-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="610" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="redis-title" value="Redis cluster" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="620" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="redis-node1" value="🔴" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#F44336;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="270" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="redis-node2" value="🔴" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#F44336;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="330" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="redis-node3" value="🔴" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#F44336;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="390" y="650" width="40" height="40" as="geometry" />
        </mxCell>

        <!-- DB集群 -->
        <mxCell id="db-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="500" y="610" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="db-title" value="DB cluster" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="570" y="620" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="db-node1" value="🗄️" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="520" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="db-node2" value="🗄️" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="580" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="db-node3" value="🗄️" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="640" y="650" width="40" height="40" as="geometry" />
        </mxCell>

        <!-- RocketMQ集群 -->
        <mxCell id="rocketmq-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="750" y="610" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="rocketmq-title" value="RocketMQ cluster" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="810" y="620" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="rocketmq-node1" value="🚀" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="770" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rocketmq-node2" value="🚀" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="830" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rocketmq-node3" value="🚀" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="890" y="650" width="40" height="40" as="geometry" />
        </mxCell>

        <!-- 右侧组件区域 -->
        <!-- 文件存储 -->
        <mxCell id="file-storage-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="450" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="file-storage-title" value="文件存储" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1050" y="460" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="file-local" value="💾 本地存储" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1010" y="485" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="file-network" value="🌐 网络存储" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1080" y="485" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="file-oss" value="☁️ OSS" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1045" y="505" width="50" height="20" as="geometry" />
        </mxCell>

        <!-- 日志收集 -->
        <mxCell id="log-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="550" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="log-title" value="日志收集" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1050" y="560" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch" value="🔍 Elasticsearch" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1010" y="585" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="logstash" value="📊 Logstash" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#FF9800;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1100" y="585" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kibana" value="📈 Kibana" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#9C27B0;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1045" y="605" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 中间件 -->
        <mxCell id="middleware-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="650" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="middleware-title" value="中间件" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1050" y="660" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="sentinel" value="🛡️ Sentinel" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1010" y="685" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="seata" value="💳 SEATA" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#4CAF50;strokeColor=none;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1080" y="685" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 技术栈说明 -->
        <mxCell id="tech-stack" value="技术栈：Java 8 + Spring Boot 2.1.9 + Dubbo 2.7.15 + Redis + MySQL/Oracle + 泛微Ecology + Gradle构建" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="200" y="750" width="800" height="30" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>