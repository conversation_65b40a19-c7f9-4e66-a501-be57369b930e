# 泛微Ecology项目部署架构图说明

## 📋 架构概述

本部署架构图展示了泛微Ecology二次开发项目在生产环境中的完整部署方案，采用分层架构设计，确保系统的高可用性、可扩展性和安全性。

## 🏗️ 架构层次详解

### 1. 客户端层 (Client Layer)
**位置**: 架构图顶部
**背景色**: 浅蓝色 (#E3F2FD)

#### 组件说明:
- **PC端浏览器**: 企业用户通过Web浏览器访问系统
- **移动端应用**: 支持移动办公，提供响应式界面
- **第三方系统**: 外部系统通过API接口集成

#### 访问特点:
- 支持多终端接入
- 统一的用户体验
- 安全的访问控制

### 2. 网关层 (Gateway Layer)
**位置**: 客户端层下方
**背景色**: 浅绿色 (#C8E6C9)
**网络区域**: DMZ区域

#### 组件说明:
- **NGINX负载均衡器**: 
  - 前端服务器负载均衡
  - 静态资源缓存
  - SSL终端处理
  - 请求分发和故障转移

- **安全网关**:
  - 统一安全认证入口
  - 防火墙功能
  - 入侵检测
  - 访问日志记录

- **API网关**:
  - API访问控制
  - 请求限流
  - 接口版本管理
  - 监控和统计

#### 安全特性:
- 位于DMZ区域，隔离内外网
- 多层安全防护
- 统一的访问入口

### 3. 服务层 (Service Layer)
**位置**: 网关层下方
**背景色**: 浅蓝色 (#BBDEFB)
**网络区域**: 内网区域

#### 核心平台:
- **泛微Ecology核心平台**:
  - OA系统核心功能
  - 用户权限管理
  - 工作流引擎
  - 基础数据管理

#### 应用集群:
- **Spring Boot应用集群**:
  - 多实例部署 (实例1、实例2)
  - 自动负载均衡
  - 健康检查
  - 滚动更新支持

- **Dubbo RPC服务集群**:
  - 人员同步服务
  - 组织同步服务
  - 微服务架构
  - 服务注册发现

#### 认证服务:
- **Portal认证中心**:
  - 企业门户认证
  - SSO单点登录
  - Token管理
  - 用户会话控制

#### 业务服务模块:
- **发票管理**: 财务发票处理和管理
- **园区管理**: 工位楼宇空间管理
- **法务管理**: 法律案件流程管理
- **申诉处理**: 诉求反馈处理流程
- **档案管理**: 企业档案信息管理
- **拼车服务**: 员工拼车功能支持
- **工作流集成**: 泛微工作流引擎集成
- **消息推送**: 实时通知和提醒

### 4. 数据层 (Data Layer)
**位置**: 服务层下方
**背景色**: 浅橙色 (#FFE0B2)

#### 数据库集群:
- **MySQL/Oracle主库**:
  - 主要数据存储
  - 写操作处理
  - 事务一致性保证

- **MySQL/Oracle从库**:
  - 读操作分离
  - 数据备份
  - 故障恢复支持

#### 缓存集群:
- **Redis主节点**:
  - 缓存数据存储
  - 会话管理
  - 分布式锁

- **Redis从节点**:
  - 缓存数据备份
  - 读写分离
  - 高可用保障

#### 文件存储:
- **NFS/OSS文件存储**:
  - 文档附件存储
  - 静态资源管理
  - 分布式文件系统

### 5. 基础设施层 (Infrastructure Layer)
**位置**: 架构图底部
**背景色**: 浅灰色 (#F5F5F5)

#### 组件说明:
- **配置中心**:
  - 环境配置管理
  - 配置热更新
  - 多环境隔离

- **监控系统**:
  - 应用性能监控
  - 系统资源监控
  - 告警通知

- **日志收集(ELK)**:
  - 日志统一收集
  - 日志分析和检索
  - 运维问题排查

- **定时任务调度器**:
  - 后台任务调度
  - 数据同步任务
  - 定期清理任务

## 🔄 数据流向

### 请求流向:
1. **客户端** → **网关层** → **服务层** → **数据层**
2. 通过NGINX负载均衡分发请求
3. 安全网关进行认证和授权
4. 服务层处理业务逻辑
5. 数据层提供数据支持

### 连接类型:
- **HTTP/HTTPS**: 客户端到网关的标准Web协议
- **RPC调用**: 服务间的Dubbo远程调用
- **数据访问**: 服务到数据库的数据操作

## 🛡️ 安全架构

### 网络安全:
- **DMZ区域**: 网关层部署在DMZ区域，隔离内外网
- **内网区域**: 服务层和数据层部署在内网，提供额外安全保护
- **防火墙**: 多层防火墙保护，控制网络访问

### 认证授权:
- **SSO单点登录**: 统一身份认证
- **API网关**: 接口访问控制
- **权限管理**: 基于角色的访问控制

## 🚀 高可用设计

### 集群部署:
- **应用集群**: Spring Boot多实例部署
- **数据库集群**: 主从复制，读写分离
- **缓存集群**: Redis主从架构
- **负载均衡**: NGINX实现请求分发

### 故障恢复:
- **健康检查**: 自动检测服务状态
- **故障转移**: 自动切换到健康实例
- **数据备份**: 定期数据备份和恢复

## 📊 技术栈

- **开发语言**: Java 8
- **应用框架**: Spring Boot 2.1.9
- **微服务**: Dubbo 2.7.15
- **数据库**: MySQL/Oracle/PostgreSQL
- **缓存**: Redis
- **构建工具**: Gradle
- **核心平台**: 泛微Ecology系统

## 🔧 运维特性

### 监控告警:
- 应用性能实时监控
- 系统资源使用监控
- 异常情况自动告警

### 日志管理:
- 统一日志收集和分析
- 分布式链路追踪
- 问题快速定位

### 配置管理:
- 集中化配置管理
- 环境配置隔离
- 配置变更审计

## 📈 扩展性

### 水平扩展:
- 服务实例可按需增减
- 数据库支持分库分表
- 缓存支持集群扩展

### 模块化设计:
- 业务服务独立部署
- 微服务架构支持
- 插件化功能扩展

这个部署架构图为泛微Ecology二次开发项目提供了完整的生产环境部署指导，确保系统的稳定性、安全性和可扩展性。
