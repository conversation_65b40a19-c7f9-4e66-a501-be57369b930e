<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="泛微Ecology部署架构图" id="deployment-architecture">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 环境标识 -->
        <mxCell id="env-label" value="生产环境部署架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1200" y="20" width="200" height="40" as="geometry" />
        </mxCell>
        
        <!-- 客户端层 -->
        <mxCell id="client-layer" value="客户端层" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- PC端浏览器 -->
        <mxCell id="pc-browser" value="PC端浏览器" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="200" y="70" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 移动端应用 -->
        <mxCell id="mobile-app" value="移动端应用" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="320" y="70" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 第三方系统 -->
        <mxCell id="third-party" value="第三方系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E3F2FD;strokeColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="440" y="70" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 网关层 -->
        <mxCell id="gateway-layer" value="网关层" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#C8E6C9;strokeColor=#4CAF50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="180" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- NGINX负载均衡 -->
        <mxCell id="nginx-lb" value="NGINX&#xa;负载均衡器" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="200" y="160" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- 安全网关 -->
        <mxCell id="security-gateway" value="安全网关" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="160" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- API网关 -->
        <mxCell id="api-gateway" value="API网关" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fontSize=12;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="440" y="160" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- 服务层 -->
        <mxCell id="service-layer" value="服务层" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="300" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 泛微Ecology核心平台 -->
        <mxCell id="ecology-core" value="泛微Ecology&#xa;核心平台" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="200" y="280" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Spring Boot应用集群 -->
        <mxCell id="springboot-cluster" value="Spring Boot应用集群" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="340" y="270" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Spring Boot实例1 -->
        <mxCell id="springboot-1" value="Spring Boot&#xa;实例1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="350" y="290" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Spring Boot实例2 -->
        <mxCell id="springboot-2" value="Spring Boot&#xa;实例2" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="450" y="290" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Dubbo RPC服务集群 -->
        <mxCell id="dubbo-cluster" value="Dubbo RPC服务集群" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="560" y="270" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Dubbo服务1 -->
        <mxCell id="dubbo-1" value="人员同步&#xa;服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="570" y="290" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Dubbo服务2 -->
        <mxCell id="dubbo-2" value="组织同步&#xa;服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="670" y="290" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Portal认证中心 -->
        <mxCell id="portal-auth" value="Portal&#xa;认证中心" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="780" y="280" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 数据层 -->
        <mxCell id="data-layer" value="数据层" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据库集群 -->
        <mxCell id="db-cluster" value="数据库集群" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="200" y="400" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 主数据库 -->
        <mxCell id="db-master" value="MySQL/Oracle&#xa;主库" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=10;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="220" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- 从数据库 -->
        <mxCell id="db-slave" value="MySQL/Oracle&#xa;从库" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=10;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Redis缓存集群 -->
        <mxCell id="redis-cluster" value="Redis缓存集群" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#FFE0B2;strokeColor=#F57C00;strokeWidth=2;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="420" y="400" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Redis主节点 -->
        <mxCell id="redis-master" value="Redis&#xa;主节点" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=10;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="440" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Redis从节点 -->
        <mxCell id="redis-slave" value="Redis&#xa;从节点" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=10;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="540" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- 文件存储 -->
        <mxCell id="file-storage" value="文件存储&#xa;(NFS/OSS)" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="640" y="400" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 基础设施层 -->
        <mxCell id="infra-layer" value="基础设施层" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="540" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 配置中心 -->
        <mxCell id="config-center" value="配置中心" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="200" y="520" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 监控系统 -->
        <mxCell id="monitoring" value="监控系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="520" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 日志收集 -->
        <mxCell id="log-collection" value="日志收集&#xa;(ELK)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="440" y="520" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 定时任务调度 -->
        <mxCell id="scheduler" value="定时任务&#xa;调度器" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#9E9E9E;strokeColor=#616161;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="560" y="520" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 - 客户端到网关 -->
        <mxCell id="client-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="pc-browser" target="nginx-lb">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="250" y="150" as="sourcePoint" />
            <mxPoint x="300" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="mobile-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="mobile-app" target="security-gateway">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="150" as="sourcePoint" />
            <mxPoint x="420" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="third-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="third-party" target="api-gateway">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="150" as="sourcePoint" />
            <mxPoint x="540" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 连接线 - 网关到服务 -->
        <mxCell id="gateway-to-service" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="nginx-lb" target="ecology-core">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="250" y="260" as="sourcePoint" />
            <mxPoint x="300" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="gateway-to-springboot" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="security-gateway" target="springboot-cluster">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="260" as="sourcePoint" />
            <mxPoint x="420" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 连接线 - 服务到数据 -->
        <mxCell id="service-to-db" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="ecology-core" target="db-cluster">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="380" as="sourcePoint" />
            <mxPoint x="310" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="service-to-redis" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="springboot-cluster" target="redis-cluster">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="380" as="sourcePoint" />
            <mxPoint x="490" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- RPC连接线 -->
        <mxCell id="rpc-connection" value="Dubbo RPC" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;strokeStyle=dashed;" edge="1" parent="1" source="springboot-cluster" target="dubbo-cluster">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="320" as="sourcePoint" />
            <mxPoint x="590" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 监控连接线 -->
        <mxCell id="monitoring-connection" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#9E9E9E;strokeWidth=1;strokeStyle=dotted;" edge="1" parent="1" source="monitoring" target="springboot-cluster">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="500" as="sourcePoint" />
            <mxPoint x="420" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 技术栈说明 -->
        <mxCell id="tech-stack" value="技术栈：Java 8 + Spring Boot 2.1.9 + Dubbo 2.7.15 + Redis + MySQL/Oracle + 泛微Ecology + Gradle构建" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="200" y="620" width="700" height="40" as="geometry" />
        </mxCell>
        
        <!-- 网络标识 -->
        <mxCell id="network-dmz" value="DMZ区域" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#FFF3E0;strokeColor=#FF9800;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="180" y="140" width="380" height="120" as="geometry" />
        </mxCell>

        <mxCell id="network-internal" value="内网区域" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="180" y="260" width="740" height="280" as="geometry" />
        </mxCell>

        <!-- 业务服务详细说明 -->
        <mxCell id="business-services" value="核心业务服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#E1F5FE;strokeColor=#0277BD;strokeWidth=1;strokeStyle=dashed;" vertex="1" parent="1">
          <mxGeometry x="950" y="270" width="300" height="100" as="geometry" />
        </mxCell>

        <!-- 发票管理服务 -->
        <mxCell id="invoice-service" value="发票管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="960" y="290" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 园区管理服务 -->
        <mxCell id="park-service" value="园区管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1040" y="290" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 法务管理服务 -->
        <mxCell id="legal-service" value="法务管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1120" y="290" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 申诉处理服务 -->
        <mxCell id="appeal-service" value="申诉处理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1200" y="290" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 档案管理服务 -->
        <mxCell id="archive-service" value="档案管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="960" y="330" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 拼车服务 -->
        <mxCell id="carpool-service" value="拼车服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1040" y="330" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 工作流服务 -->
        <mxCell id="workflow-service" value="工作流集成" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1120" y="330" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 消息推送服务 -->
        <mxCell id="message-service" value="消息推送" style="rounded=1;whiteSpace=wrap;html=1;fontSize=10;fillColor=#03A9F4;strokeColor=#0277BD;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1200" y="330" width="70" height="30" as="geometry" />
        </mxCell>

        <!-- 高可用标识 -->
        <mxCell id="ha-label" value="高可用集群部署" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#D32F2F;" vertex="1" parent="1">
          <mxGeometry x="950" y="380" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 负载均衡标识 -->
        <mxCell id="lb-label" value="负载均衡" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="220" y="250" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 数据流向标识 -->
        <mxCell id="flow-label-1" value="HTTP/HTTPS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="120" y="140" width="70" height="20" as="geometry" />
        </mxCell>

        <mxCell id="flow-label-2" value="RPC调用" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="580" y="250" width="50" height="20" as="geometry" />
        </mxCell>

        <mxCell id="flow-label-3" value="数据访问" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="320" y="380" width="60" height="20" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
