# Draw.io架构图使用说明

## 文件说明

本项目提供了两个版本的Draw.io架构图文件：

1. **项目业务架构图.drawio** - 详细版本，包含完整的模块和连接线
2. **项目业务架构图_优化版.drawio** - 优化版本，布局更清晰，推荐使用

## 如何使用

### 方法一：在线使用
1. 访问 [draw.io](https://app.diagrams.net/) 官网
2. 点击 "Open Existing Diagram"
3. 选择 "Device" 并上传 `.drawio` 文件
4. 即可在线编辑和查看架构图

### 方法二：本地使用
1. 下载 [draw.io Desktop](https://github.com/jgraph/drawio-desktop/releases) 应用
2. 安装后直接打开 `.drawio` 文件
3. 支持离线编辑和导出

## 架构图特点

### 布局优化
- **画布尺寸**: 2200x1600像素，适合大屏显示
- **层级间距**: 每层间距160-240像素，层次清晰
- **模块尺寸**: 统一的模块大小，视觉协调
- **字体大小**: 16-20像素，清晰易读

### 颜色方案
- **外部系统层**: 淡灰色背景 (#f5f5f5)
- **用户界面层**: 淡蓝色背景 (#e3f2fd)
- **应用服务层**: 淡绿色背景 (#e8f5e8)
- **基础服务层**: 淡黄色背景 (#fff8e1)
- **数据持久层**: 淡紫色背景 (#f3e5f5)
- **边框颜色**: 统一绿色 (#4caf50)

### 连接线说明
- **实线箭头**: 主要数据流向，绿色粗线
- **虚线箭头**: 层级关系，蓝色虚线
- **箭头方向**: 从上到下，表示调用关系

## 架构层次详解

### 1. 外部系统层
- 泛微Ecology系统：核心OA平台
- 安全网关：统一安全认证
- Portal认证中心：企业门户
- 移动端应用：移动办公支持

### 2. 用户界面层
- RESTful API：标准REST接口
- Controller层：请求处理
- SSO单点登录：统一认证
- 权限控制：访问控制
- 异常处理：错误处理

### 3. 应用服务层
**核心业务服务**：
- 发票管理服务：财务发票处理
- 园区管理服务：工位楼宇管理
- 法务管理服务：法律案件管理
- 申诉处理服务：诉求流程处理
- 档案管理服务：档案信息管理
- 拼车服务：拼车功能支持

**集成服务**：
- 工作流集成：泛微工作流
- RPC远程服务：Dubbo服务调用
- 定时任务：后台任务处理
- 消息推送：通知提醒

### 4. 基础服务层
**数据服务**：
- 数据访问服务：统一数据访问
- 缓存服务：Redis缓存
- 导出服务：Excel/PDF导出

**工具组件**：
- HTTP工具、加密工具、分页工具
- 转换工具、日期工具、SSO工具
- 字符串工具、集合工具、SQL工具
- 工作流工具、短信工具

### 5. 数据持久层
- EBuilder表单：泛微表单引擎
- 数据库访问：多数据库支持
- 文件存储：文件管理
- 配置管理：系统配置
- 多租户支持：租户隔离

## 编辑建议

### 添加新模块
1. 复制现有模块样式
2. 保持统一的尺寸和颜色
3. 放置在合适的层级中
4. 添加必要的连接线

### 修改布局
1. 选中整个层级进行移动
2. 保持层级间的间距一致
3. 确保模块不重叠
4. 调整连接线路径

### 导出格式
- **PNG**: 适合文档插入，推荐300DPI
- **SVG**: 矢量格式，适合网页显示
- **PDF**: 适合打印和分享
- **VSDX**: 兼容Visio格式

## 技术规范

### 模块规范
- **尺寸**: 140-200像素宽，50-60像素高
- **边框**: 2像素，绿色 (#4caf50)
- **字体**: 14-16像素，黑色
- **圆角**: 3像素

### 层级规范
- **边框**: 3像素，绿色
- **透明度**: 80%
- **标题**: 20像素，粗体
- **间距**: 层级间40像素间距

### 连接线规范
- **主流程**: 4像素粗，绿色实线
- **层级关系**: 3像素，蓝色虚线
- **箭头**: 经典箭头样式

## 维护建议

1. **定期更新**: 随着项目发展更新架构图
2. **版本控制**: 保存不同版本的架构图
3. **文档同步**: 确保架构图与文档一致
4. **团队共享**: 在团队中共享最新版本

## 常见问题

**Q: 如何调整模块大小？**
A: 选中模块，拖拽边角调整，或在右侧属性面板设置精确尺寸。

**Q: 如何对齐模块？**
A: 选中多个模块，使用顶部工具栏的对齐工具。

**Q: 如何修改颜色？**
A: 选中元素，在右侧样式面板中修改填充色和边框色。

**Q: 如何导出高质量图片？**
A: 文件 → 导出 → PNG，设置DPI为300，勾选"透明背景"。

**Q: 如何添加图例？**
A: 在空白区域添加文本和形状，说明不同颜色和符号的含义。
