<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
    http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <dubbo:service ref="cmicSynHrRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_syn_hr_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicSynOrgRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_syn_org_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicTodoTabRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_todo_tab_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicPutDataToTrade100Action"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_put_data_to_trade100_action" version="1.0"/>
    <dubbo:service ref="cmicHotelSyncAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_hotel_sync_action" version="1.0"/>
    <dubbo:service ref="cmicTodoDataRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_todo_data_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicTokenRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_token_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicTodoGrayRpcAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_todo_gray_rpc_action" version="1.0"/>
    <dubbo:service ref="cmicSyncWorkflowTodoAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_sync_workflow_todo_action" version="1.0"/>
    <dubbo:service ref="cmicJudgmentDocUpAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_judgment_doc_up_action" version="1.0"/>
    <dubbo:service ref="cmicFinanceCheckedAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_finance_checked_action" version="1.0"/>
    <dubbo:service ref="cmicTheCheckedFlowAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_the_checked_flow_action" version="1.0"/>
    <dubbo:service ref="cmicFirstTopicEstablishmentAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_first_topic_establishment_action" version="1.0"/>
    <dubbo:service ref="cmicStationNoteRemindAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_station_note_remind_action" version="1.0"/>
    <dubbo:service ref="cmicArea6GongWeiAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_area6_gong_wei_action" version="1.0"/>
    <dubbo:service ref="cmicSmsGongSendAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_sms_gong_send_action" version="1.0"/>
    <dubbo:service ref="endToEndCoOvertimeAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_end_to_end_co_overtime_action" version="1.0"/>
    <dubbo:service ref="endToEndOvertimeAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_end_to_end_overtime_action" version="1.0"/>
    <dubbo:service ref="notificationOfOverdueFileAction"
                   interface="com.weaver.esb.api.rpc.EsbServerlessRpcRemoteInterface"
                   group="cmic_notification_of_overdue_file_action" version="1.0"/>
</beans>
